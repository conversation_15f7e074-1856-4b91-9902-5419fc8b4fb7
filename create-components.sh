#!/bin/bash

# Verifica si se proporcionó al menos un nombre
if [ $# -eq 0 ]; then
    echo "❌ Por favor, proporciona una lista de nombres en minúsculas."
    exit 1
fi

# Función para capitalizar la primera letra
capitalize() {
    echo "$1" | awk '{print toupper(substr($0,1,1))tolower(substr($0,2))}'
}

# Itera sobre cada nombre proporcionado
for name in "$@"; do
    # Convertir el nombre para los archivos y carpetas
    folder_name=$name
    component_name=$(capitalize "$name")

    # Define la ruta de la carpeta
    component_dir="src/pages/dashboard/$folder_name"

    # Crea la carpeta
    mkdir -p $component_dir

    # Crea el archivo principal del componente
    cat <<EOL >$component_dir/$component_name.tsx

import { useLoaderData } from 'react-router-dom';
import { DashboardContentHeader } from "../../../components";

export const $component_name: React.FC = () => {
    const $name = useLoaderData() as any
    console.log($name)
    return (
        <>
      <DashboardContentHeader
        title="$component_name"
        tooltipTitle="Create $name"
        onClick={() => console.log("Clicked")}
      />
    </>
    );
};
EOL

    # Crea el archivo de rutas
    cat <<EOL >$component_dir/${component_name}Route.tsx

import { RouteObject } from "react-router-dom";
import { ${component_name} } from "./${component_name}";
import { ${name}Loader } from "./${name}Loader";

export const ${name}Route: RouteObject = {
  path: "${name}",
  element: <${component_name} />,
  loader: ${name}Loader
};
EOL

    # Crea el archivo de loader
    cat <<EOL >$component_dir/${name}loader.ts

import { LoaderFunction } from 'react-router-dom';

export const ${name}Loader: LoaderFunction = async (): Promise<any> => {
  return await "test"
};
EOL

    echo "✅ Componente $component_name creado exitosamente en $component_dir"
done
