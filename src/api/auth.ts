import { config } from "../config/configuration";
import {
  AuthRequest,
  AuthResponse,
  ConfirmPasswordRequest,
  ConfirmPasswordResponse,
} from "../interfaces/auth";
import { hoaClient } from "./api-clients";

export const confirmPassword = async (
  confirmationPasswordRequest: ConfirmPasswordRequest
) => {
  try {
    const response = await hoaClient.post<ConfirmPasswordResponse>(
      `${config.baseUrl}/auth/confirm-password`,
      confirmationPasswordRequest
    );

    return response;
  } catch (error) {
    console.log(error);
  }
};

export const authenticate = async (authRequest: AuthRequest) => {
  try {
    const response = await hoaClient.post<AuthResponse>(
      `${config.baseUrl}/auth`,
      authRequest
    );
    return response.data;
  } catch (error) {
    console.error("Error during login:", error);
    return null;
  }
};
