import { ParkingSpot } from "../interfaces/parking-spot";
import { hoaClient } from "./api-clients";

export const getParkingSpots = async (): Promise<ParkingSpot[]> => {
  try {
    const response = await hoaClient.get<ParkingSpot[]>("/parking-spot");
    return response.data;
  } catch (error) {
    console.error("Error al obtener los espacios de estacionamiento:", error);
    throw new Error(
      "No se pudieron obtener los espacios de estacionamiento. Intente de nuevo."
    );
  }
};

export const updateParkingSpot = async (
  parkingSpot: ParkingSpot
): Promise<ParkingSpot> => {
  try {
    const response = await hoaClient.patch<ParkingSpot>(
      `/parking-spot/${parkingSpot.id}`,
      {
        spotNumber: parkingSpot.spotNumber,
        isAvailable: parkingSpot.isAvailable,
        type: parkingSpot.type,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error al crear espacio de estacionamiento:", error);
    throw new Error(
      "No se pudo crear el espacio de estacionamiento. Intente nuevamente."
    );
  }
};

export const createParkingSpot = async (
  parkingSpot: Omit<ParkingSpot, "id">
): Promise<ParkingSpot> => {
  try {
    const response = await hoaClient.post<ParkingSpot>("/parking-spot", {
      spotNumber: parkingSpot.spotNumber,
      isAvailable: parkingSpot.isAvailable,
      type: parkingSpot.type,
    });
    return response.data;
  } catch (error) {
    console.error("Error al crear espacio de estacionamiento:", error);
    throw new Error(
      "No se pudo crear el espacio de estacionamiento. Intente nuevamente."
    );
  }
};
