import { Role } from "../interfaces/role";
import { hoaClient } from "./api-clients";

export const getRoles = async (): Promise<Role[]> => {
  try {
    const response = await hoaClient.get<Role[]>("/role");
    return response.data;
  } catch (error) {
    console.error("Error al obtener roles:", error);
    throw new Error("No se pudieron obtener los roles. Intente de nuevo.");
  }
};

export const updateRole = async (role: Role): Promise<Role> => {
  try {
    const response = await hoaClient.patch<Role>(`/role/${role.id}`, {
      name: role.name,
      description: role.description,
    });
    return response.data;
  } catch (error) {
    console.error(`Error al actualizar el rol ${role.id}:`, error);
    throw new Error(
      "No se pudo actualizar el rol. Verifique los datos e intente de nuevo."
    );
  }
};

export const createRole = async (role: Omit<Role, "id">): Promise<Role> => {
  try {
    const response = await hoaClient.post<Role>("/role", {
      name: role.name,
      description: role.description,
    });
    return response.data;
  } catch (error) {
    console.error("Error al crear rol:", error);
    throw new Error("No se pudo crear el rol. Intente nuevamente.");
  }
};
