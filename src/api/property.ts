import { Property } from "../interfaces/property";
import { hoaClient } from "./api-clients";

export const getProperties = async (): Promise<Property[]> => {
  try {
    const response = await hoaClient.get<Property[]>("/property");
    return response.data;
  } catch (error) {
    console.error("Error al obtener las propiedades:", error);
    throw new Error(
      "No se pudieron obtener las propiedades. Intente de nuevo."
    );
  }
};

export const getProperty = async (
  propertyId: Property["id"]
): Promise<Property> => {
  const response = await hoaClient.get<Property>(`/property/${propertyId}`);
  return response.data;
};

export const updateProperty = async (property: Property): Promise<Property> => {
  try {
    const response = await hoaClient.patch<Property>(
      `/property/${property.id}`,
      {
        address: property.address,
        residents: property.residents,
        reservations: property.reservations,
        rentals: property.rentals,
        infractions: property.infractions,
        payments: property.payments,
        visits: property.visits,
        parkingSpots: property.parkingSpots,
        pets: property.pets,
        vehicles: property.vehicles,
        tags: property.tags,
      }
    );
    return response.data;
  } catch (error) {
    console.error(`Error al actualizar la propiedad ${property.id}:`, error);
    throw new Error(
      "No se pudo actualizar la propiedad. Verifique los datos e intente de nuevo."
    );
  }
};

export const createProperty = async (
  property: Omit<Property, "id">
): Promise<Property> => {
  try {
    const response = await hoaClient.post<Property>("/property", {
      address: property.address,
      residents: property.residents,
      reservations: property.reservations,
      rentals: property.rentals,
      infractions: property.infractions,
      payments: property.payments,
      visits: property.visits,
      parkingSpots: property.parkingSpots,
      pets: property.pets,
      vehicles: property.vehicles,
      tags: property.tags,
    });
    return response.data;
  } catch (error) {
    console.error("Error al crear la propiedad:", error);
    throw new Error("No se pudo crear la propiedad. Intente nuevamente.");
  }
};
