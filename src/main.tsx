import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { App } from "./App";
import { ConfigProvider } from "antd";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ConfigProvider
      theme={{
        token: {
          // Seed Token
          colorPrimary: "#237A91",
          borderRadius: 12,
          colorError: "#E97C5D",
          // Alias Token
          colorBgContainer: "#ffffff",
        },
      }}
    >
      <App />
    </ConfigProvider>
  </StrictMode>
);
