import { Layout } from "antd";
import { AppRouter } from "./router/AppRouter";
import { Content } from "antd/es/layout/layout";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

export const App: React.FC = () => {
  return (
    <Layout
      style={{
        minHeight: "100vh",
        width: "100vw",
        background: "linear-gradient(to right, #1B4959 0%, #DFD6C6 100%)",
      }}
    >
      <Content
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <QueryClientProvider client={queryClient}>
          <AppRouter />
        </QueryClientProvider>
      </Content>
    </Layout>
  );
};
