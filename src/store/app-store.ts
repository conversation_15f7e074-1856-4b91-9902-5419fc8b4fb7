import { makeAutoObservable, runInAction } from "mobx";
import { Property } from "../interfaces/property";
import { Role } from "../interfaces/role";
import { getRoles } from "../api/roles";
import { ParkingSpot } from "../interfaces/parking-spot";

class AppStore {
  properties: Property[] = [];
  roles: Role[] = [];
  parkingSpots: ParkingSpot[] = [];
  isLoading = false;
  error: string | null = null;

  constructor() {
    makeAutoObservable(this);
  }

  async fetchRoles() {
    this.isLoading = true;
    this.error = null;
    try {
      const roles = await getRoles();
      runInAction(() => {
        this.roles = roles;
        this.isLoading = false;
      });
    } catch (error) {
      runInAction(() => {
        this.error = "Error fetching roles";
        this.isLoading = false;
      });
    }
  }

  async fetchInitialData() {
    await this.fetchRoles();
  }
}

export const appStore = new AppStore();
