import React from "react";
import { List, Typography, Space, Button, Tag } from "antd";
import { EditOutlined } from "@ant-design/icons";
import { Facility } from "../../../interfaces/facility";
import { ActionEntityButton } from "../../buttons/ActionEntityButton";

const { Text } = Typography;

interface FaclitiesListProps {
  facilities: Facility[];
  onEdit: (facility: Facility) => void;
}

export const FacilitiesList: React.FC<FaclitiesListProps> = ({
  facilities,
  onEdit,
}) => {
  return (
    <div style={{ maxWidth: "600px", margin: "0 auto" }}>
      <List
        itemLayout="horizontal"
        dataSource={facilities}
        renderItem={(facility) => (
          <List.Item
            actions={[
              <Button
                key={facility.id}
                icon={<EditOutlined />}
                onClick={() => onEdit(facility)}
                type="link"
              >
                Editar
              </Button>,
            ]}
          >
            <List.Item.Meta
              avatar={facility.name.slice(0, 2)}
              title={
                <ActionEntityButton
                  data={facility}
                  getUrl={(facility) => `/dashboard/facilities/${facility.id}`}
                  getLabel={(facility) => facility.name}
                  emptyText="Sin Amenidades"
                  type="link"
                />
              }
              description={
                <Space direction="vertical">
                  <Text>{facility.description}</Text>
                </Space>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );
};
