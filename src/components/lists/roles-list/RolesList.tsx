import { List, Tag, Button, Space, Typography } from "antd";
import { EditOutlined } from "@ant-design/icons";
import { Role } from "../../../interfaces/role";

const { Text } = Typography;

interface RolesListProps {
  roles: Role[];
  onEdit: (role: Role) => void;
}

export const RolesList: React.FC<RolesListProps> = ({ roles, onEdit }) => {
  return (
    <div style={{ maxWidth: "600px", margin: "0 auto" }}>
      <List
        itemLayout="horizontal"
        dataSource={roles}
        renderItem={(role) => (
          <List.Item
            actions={[
              <Button
                icon={<EditOutlined />}
                onClick={() => onEdit(role)}
                type="link"
              >
                Editar
              </Button>,
            ]}
          >
            <List.Item.Meta
              avatar={role.name.slice(0, 2)}
              title={<Tag color="green">{role.name}</Tag>}
              description={
                <Space direction="vertical">
                  <Text>{role.description}</Text>
                </Space>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );
};
