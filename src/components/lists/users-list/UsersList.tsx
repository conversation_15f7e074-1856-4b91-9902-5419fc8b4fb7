import React from "react";
import { List, Avatar, Typography, Space, Button, Tag } from "antd";
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  EditOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import { User } from "../../../interfaces/user";

const { Text } = Typography;

interface UserListProps {
  users: User[];
  onEdit: (user: any) => void;
}

export const UsersList: React.FC<UserListProps> = ({ users, onEdit }) => {
  return (
    <List
      itemLayout="horizontal"
      dataSource={users}
      renderItem={(user) => {
        const editData = {
          ...user,
          roles: user.roles.map((role) => role.id),
          properties: user.properties.map(
            (residentProperty) => residentProperty.id
          ),
        };
        return (
          <List.Item
            actions={[
              <Button
                key={`${user.id}-edit-button`}
                icon={<EditOutlined />}
                onClick={() => onEdit(editData)}
                type="link"
              >
                Editar
              </Button>,
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar icon={<UserOutlined />} />}
              title={
                <Space>
                  <Text strong>{user.firstName}</Text>
                  <Text strong>{user.paternalLastName}</Text>
                  <Text strong>{user.maternalLastName}</Text>
                </Space>
              }
              description={
                <Space direction="vertical">
                  <Text>
                    <MailOutlined /> {user.email}
                  </Text>
                  <Text>
                    <PhoneOutlined /> {user.phone}
                  </Text>
                  {user.properties.length > 0 &&
                    user.properties.map((residentProperty) => (
                      <Text key={`${residentProperty.id}-text`}>
                        <HomeOutlined /> {residentProperty.address}
                      </Text>
                    ))}
                  {user.roles.map((role) => (
                    <Tag key={`${role.id}-tag`} id={role.name} color="green">
                      {role.name}
                    </Tag>
                  ))}
                </Space>
              }
            />
          </List.Item>
        );
      }}
    />
  );
};
