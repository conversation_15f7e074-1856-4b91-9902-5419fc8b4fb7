import { Avatar, Dropdown, MenuProps } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { Header } from "antd/es/layout/layout";

const headerStyle: React.CSSProperties = {
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  paddingRight: 12,
  background: "linear-gradient(to right, #1B4959 0%, #DFD6C6 100%)",
};

interface DashboardHeaderProps {
  avatarMenuItems: MenuProps["items"];
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  avatarMenuItems,
}) => {
  return (
    <Header style={headerStyle}>
      <Dropdown menu={{ items: avatarMenuItems }} trigger={["click"]}>
        <Avatar style={{ cursor: "pointer" }} icon={<UserOutlined />} />
      </Dropdown>
    </Header>
  );
};
