import { Button, Flex, Tooltip } from "antd";
import { ContentTitle } from "../texts";
import { PlusOutlined } from "@ant-design/icons";

interface DashboardContentHeaderProps {
  title: string;
  tooltipTitle?: string;
  onClick?: () => void;
}

export const DashboardContentHeader: React.FC<DashboardContentHeaderProps> = ({
  title,
  tooltipTitle,
  onClick,
}) => {
  return (
    <Flex justify="space-between" align="center">
      <ContentTitle text={title} />
      {onClick !== undefined && (
        <Tooltip placement="topLeft" title={tooltipTitle}>
          <Button
            type="primary"
            shape="circle"
            icon={<PlusOutlined />}
            onClick={onClick}
          />
        </Tooltip>
      )}
    </Flex>
  );
};
