import { Menu, MenuProps } from "antd";
import Sider from "antd/es/layout/Sider";

import { useState } from "react";

const siderStyle: React.CSSProperties = {
  height: "100vh",
  position: "sticky",
  left: 0,
  bottom: 0,
  top: 0,
};

interface DashboardSiderProps {
  menuItems: MenuProps["items"];
}

export const DashboardSider: React.FC<DashboardSiderProps> = ({
  menuItems,
}) => {
  const [collapsed, setCollapsed] = useState(true);

  return (
    <Sider
      style={siderStyle}
      theme="light"
      trigger={null}
      collapsible
      collapsed={collapsed}
      onPointerOver={() => setCollapsed(false)}
      onPointerOut={() => setCollapsed(true)}
    >
      <Menu mode="inline" defaultSelectedKeys={["1"]} items={menuItems} />
    </Sider>
  );
};
