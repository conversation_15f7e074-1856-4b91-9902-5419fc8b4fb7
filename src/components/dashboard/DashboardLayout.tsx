import { Layout, theme } from "antd";
import { Content } from "antd/es/layout/layout";
import { DashboardSider } from "./DashboardSider";
import { DashboardHeader } from "./DashboardHeader";
import { DashboardFooter } from "./DashboardFooter";
import {
  AimOutlined,
  AlertOutlined,
  ApartmentOutlined,
  AppstoreOutlined,
  BankOutlined,
  BarcodeOutlined,
  BookOutlined,
  BuildOutlined,
  CalendarOutlined,
  CarOutlined,
  CarryOutOutlined,
  CheckSquareOutlined,
  ContactsOutlined,
  CreditCardOutlined,
  DashboardOutlined,
  DeploymentUnitOutlined,
  DollarOutlined,
  DropboxOutlined,
  FileDoneOutlined,
  FlagOutlined,
  FormatPainterOutlined,
  HomeOutlined,
  IdcardOutlined,
  NotificationOutlined,
  SafetyCertificateOutlined,
  ScheduleOutlined,
  SettingOutlined,
  ShareAltOutlined,
  SmileOutlined,
  TeamOutlined,
  TruckOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";
import { Outlet, useNavigate } from "@tanstack/react-router";

const footerText = `HOA Manager ©${new Date().getFullYear()} Created by <PERSON><PERSON><PERSON>`;

const avatarMenuItems = [
  {
    key: "1",
    label: "Perfil",
    onClick: () => console.log("profile"),
  },
  {
    key: "2",
    label: "Configuración",
    onClick: () => console.log("configuration"),
  },
  {
    key: "3",
    label: "Cerrar sesión",
    onClick: () => console.log("logout"),
  },
];

export const DashboardLayout: React.FC = () => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const navigate = useNavigate();

  const siderMenuItems = [
    {
      key: "higlights",
      icon: <DashboardOutlined />,
      label: "Higlights",
      onClick: () => navigate({ to: "/dashboard/highlights" }),
    },
    {
      key: "tasks",
      label: "Tareas",
      icon: <CheckSquareOutlined />,
      onClick: () => navigate({ to: "/dashboard/tasks" }),
    },
    {
      key: "gestion",
      label: "Gestión",
      icon: <AppstoreOutlined />,
      children: [
        {
          key: "users",
          label: "Usuarios",
          icon: <IdcardOutlined />,
          onClick: () => navigate({ to: "/dashboard/users" }),
        },
        {
          key: "properties",
          label: "Propiedades",
          icon: <HomeOutlined />,
          onClick: () => navigate({ to: "/dashboard/properties" }),
        },
        {
          key: "packages",
          label: "Paquetes",
          icon: <DropboxOutlined />,
          onClick: () => navigate({ to: "/dashboard/packages" }),
        },
        {
          key: "facilities",
          icon: <SmileOutlined />,
          label: "Amenidades",
          onClick: () => navigate({ to: "/dashboard/facilities" }),
        },
        {
          key: "vehicles",
          icon: <CarOutlined />,
          label: "Vehículos",
          onClick: () => navigate({ to: "/dashboard/vehicles" }),
        },
        {
          key: "tags",
          icon: <BarcodeOutlined />,
          label: "Tags",
          onClick: () => navigate({ to: "/dashboard/tags" }),
        },
        {
          key: "parking-spots",
          label: "Estacionamiento",
          icon: <BuildOutlined />,
          onClick: () => navigate({ to: "/dashboard/parking-spots" }),
        },
        {
          key: "visits",
          label: "Visitas",
          icon: <ContactsOutlined />,
          onClick: () => navigate({ to: "/dashboard/visits" }),
        },
        {
          key: "rentals",
          label: "Rentas",
          icon: <UsergroupAddOutlined />,
          onClick: () => navigate({ to: "/dashboard/rentals" }),
        },
        {
          key: "services",
          label: "Servicios",
          icon: <DeploymentUnitOutlined />,
          onClick: () => navigate({ to: "/dashboard/services" }),
        },
        {
          key: "suppliers",
          label: "Proveedores",
          icon: <TruckOutlined />,
          onClick: () => navigate({ to: "/dashboard/suppliers" }),
        },
        {
          key: "employees",
          icon: <TeamOutlined />,
          label: "Empleados",
          onClick: () => navigate({ to: "/dashboard/employees" }),
        },
      ],
    },

    {
      key: "events",
      label: "Eventos",
      icon: <CalendarOutlined />,
      onClick: () => navigate({ to: "/dashboard/events" }),
    },
    // {
    //   key: "news",
    //   icon: <ReadOutlined />,
    //   label: "Noticias",
    //   onClick: () => navigate({ to: "/dashboard/news" }),
    // },
    {
      key: "announcements",
      label: "Comunicados",
      icon: <NotificationOutlined />,
      onClick: () => navigate({ to: "/dashboard/announcements" }),
    },
    {
      key: "maintenanceIussueRepots",
      label: "Mantenimiento",
      icon: <FormatPainterOutlined />,
      onClick: () => navigate({ to: "/dashboard/maintenance-issue-reports" }),
    },
    {
      key: "phone-directory",
      icon: <ContactsOutlined />,
      label: "Directorio",
      onClick: () => navigate({ to: "/dashboard/phone-directory" }),
    },
    {
      key: "seguridad",
      icon: <SafetyCertificateOutlined />,
      label: "Seguridad",
      children: [
        {
          key: "regulations",
          label: "Reglamentos",
          icon: <FileDoneOutlined />,
          onClick: () => navigate({ to: "/dashboard/regulations" }),
        },
        {
          key: "protocols",
          label: "Protocolos",
          icon: <ShareAltOutlined />,
          onClick: () => navigate({ to: "/dashboard/protocols" }),
        },
        {
          key: "complaints",
          icon: <AlertOutlined />,
          label: "Quejas",
          onClick: () => navigate({ to: "/dashboard/complaints" }),
        },
        {
          key: "infractions",
          icon: <FlagOutlined />,
          label: "Incumplimientos",
          onClick: () => navigate({ to: "/dashboard/infractions" }),
        },
        {
          key: "fines",
          icon: <BookOutlined />,
          label: "Multas",
          onClick: () => navigate({ to: "/dashboard/fines" }),
        },
      ],
    },

    {
      key: "reservations",
      label: "Reservaciones",
      icon: <ScheduleOutlined />,
      onClick: () => navigate({ to: "/dashboard/reservations" }),
    },

    {
      key: "finanzas",
      icon: <BankOutlined />,
      label: "Finanzas",
      children: [
        {
          key: "expenses",
          label: "Gastos",
          icon: <DollarOutlined />,
          onClick: () => navigate({ to: "/dashboard/expenses" }),
        },
        {
          key: "payments",
          label: "Pagos",
          icon: <CreditCardOutlined />,
          onClick: () => navigate({ to: "/dashboard/payments" }),
        },
        {
          key: "monthly-maintenance-charges",
          label: "Cuotas mensuales",
          icon: <CarryOutOutlined />,
          onClick: () =>
            navigate({ to: "/dashboard/monthly-maintenance-charges" }),
        },
      ],
    },
    {
      key: "configuration",
      icon: <SettingOutlined />,
      label: "Configuración",
      children: [
        {
          key: "roles",
          label: "Roles",
          icon: <ApartmentOutlined />,
          onClick: () => navigate({ to: "/dashboard/roles" }),
        },
        {
          key: "complaint-types",
          label: "Tipo de quejas",
          icon: <AimOutlined />,
          onClick: () => navigate({ to: "/dashboard/complaint-types" }),
        },
        {
          key: "maintenance-fees",
          label: "Cuotas",
          icon: <CarryOutOutlined />,
          onClick: () => navigate({ to: "/dashboard/maintenance-fees" }),
        },
      ],
    },
  ];

  return (
    <Layout>
      <DashboardSider menuItems={siderMenuItems} />
      <Layout>
        <DashboardHeader avatarMenuItems={avatarMenuItems} />
        <Content
          style={{
            margin: "24px 16px",
            padding: 20,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
            overflow: "initial",
          }}
        >
          <Outlet />
        </Content>
        <DashboardFooter footerText={footerText} />
      </Layout>
    </Layout>
  );
};
