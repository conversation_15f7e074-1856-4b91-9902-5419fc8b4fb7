import { PropertyType } from "../../interfaces/property";
import { PropertiesRow } from "./PropertiesRow";
import { VisitsParkingSpotsRow } from "./VisitsParkingSpotsRow";
import { useProperties } from "../../hooks/useQuery";
import { useParkingSpotsManger } from "../../hooks/useParkingSpotsManager";
import { useParkingSpots } from "../../hooks/useCustomQueries";

export const VisitParkingSpotsMap: React.FC = () => {
  const { listQuery: propertiesListQuery } = useProperties();
  const { visitorParkingSpotsQuery } = useParkingSpots();
  const properties = propertiesListQuery.data ?? [];
  const parkingSpots = visitorParkingSpotsQuery.data ?? [];

  const {
    houseBlocks,
    reversedHouseBlocks,
    departmentBlocks,
    parkingSpotsBlocks,
    reversedParkingSpotsBlocks,
  } = useParkingSpotsManger(properties, parkingSpots);

  return (
    <div style={{ padding: "90px", paddingTop: "40px" }}>
      {/* SVG del estacionamiento */}
      <svg
        viewBox="0 0 600 400"
        className="w-full h-auto border border-gray-200 rounded-md"
      >
        {/* Casas 1 a 8 */}
        <PropertiesRow
          properties={reversedHouseBlocks[0]}
          axisX={365}
          axisY={320}
          transformGroup="rotate(4,180,360)"
        />
        {/* Casas 1 al 8 */}
        {/* Casas 9 a 17 */}
        <PropertiesRow
          properties={reversedHouseBlocks[1]}
          axisX={50}
          axisY={346}
          transformGroup="rotate(-6,180,360)"
        />
        {/* Casas 9 a 17 */}
        {/* Casas 18 a 31 */}
        <PropertiesRow
          properties={reversedHouseBlocks[2]}
          axisX={50}
          axisY={0}
          transformGroup="rotate(-6,180,360)"
          vertical
          parkingBottom
        />
        {/* Casas 18 a 31 */}

        {/* Casas 32 a 48 */}
        <PropertiesRow
          properties={houseBlocks[3]}
          axisX={110}
          axisY={0}
          parkingBottom
        />
        {/* Casas 32 a 48 */}

        {/* Casas 49 a 61 */}
        <PropertiesRow
          properties={houseBlocks[4]}
          axisX={515}
          axisY={-23}
          transformGroup="rotate(4,180,360)"
          vertical
        />
        {/* Casas 49 a 61 */}

        {/* Casas 62 a 69 */}
        <PropertiesRow
          properties={reversedHouseBlocks[5]}
          axisX={310}
          axisY={70}
        />
        {/* Casas 62 a 69 */}

        {/* Casas 70 a 76 */}
        <PropertiesRow
          properties={reversedHouseBlocks[6]}
          axisX={115}
          axisY={70}
        />
        {/* Casas 70 a 76 */}

        {/* Casas 77 a 83 */}
        <PropertiesRow
          properties={houseBlocks[7]}
          axisX={118}
          axisY={112}
          parkingBottom
        />
        {/* Casas 77 a 83 */}

        {/* Casas 84 a 91 */}
        <PropertiesRow
          properties={houseBlocks[8]}
          axisX={306}
          axisY={112}
          parkingBottom
        />

        {/* Casas 84 a 91 */}

        {/* Casas 92 a 99 */}
        <PropertiesRow
          properties={reversedHouseBlocks[9]}
          axisX={300}
          axisY={185}
        />
        {/* Casas 92 a 99 */}

        {/* Casas 100 a 106 */}
        <PropertiesRow
          properties={reversedHouseBlocks[10]}
          axisX={125}
          axisY={185}
        />
        {/* Casas 100 a 106 */}

        {/* Casa107 */}
        <PropertiesRow
          properties={houseBlocks[11]}
          axisX={127}
          axisY={226}
          transformGroup="rotate(-1,180,360)"
          vertical
        />
        {/* Casa 107 */}

        {/* Casa 108 */}
        <PropertiesRow
          properties={houseBlocks[12]}
          axisX={137}
          axisY={255}
          transformGroup="rotate(-6,180,360)"
          vertical
        />
        {/* Casas108 */}

        {/* Casas 109 a 113 */}
        <PropertiesRow
          properties={houseBlocks[13]}
          axisX={137}
          axisY={278}
          transformGroup="rotate(-6,180,360)"
          parkingBottom
        />
        {/* Casas 109 a 113 */}

        {/* Casas 114 a 116 */}
        <PropertiesRow
          properties={houseBlocks[14]}
          axisX={373}
          axisY={267}
          parkingBottom
        />
        {/* Casas 114 a 116 */}
        {/* Casa 117 a 118 */}
        <PropertiesRow
          properties={reversedHouseBlocks[15]}
          axisX={442}
          axisY={260}
          parkingBottom
          vertical
        />
        {/* Casa 117 a 118 */}
        {/* Casa 119 */}
        <PropertiesRow
          properties={houseBlocks[16]}
          axisX={442}
          axisY={227}
          parkingBottom
          vertical
        />
        {/* Casa 119 */}

        <g transform="translate(249, 254) rotate(-2, 180, 360)">
          <rect
            x={9}
            y={-9}
            width={124}
            height={60}
            stroke="#000"
            fill="#bec5c9"
            strokeWidth="1"
          />
          <rect
            x={70}
            y={-9}
            width={1}
            height={60}
            stroke="#000"
            strokeWidth="1"
          />
          <PropertiesRow
            properties={departmentBlocks[0]}
            axisX={119}
            axisY={-9}
            type={PropertyType.DEPARTMENT}
            vertical
            parkingBottom
          />

          <PropertiesRow
            properties={departmentBlocks[1]}
            axisX={60}
            axisY={-9}
            type={PropertyType.DEPARTMENT}
            vertical
          />

          <PropertiesRow
            properties={departmentBlocks[2]}
            axisX={60}
            axisY={27}
            type={PropertyType.DEPARTMENT}
            vertical
          />

          <PropertiesRow
            properties={departmentBlocks[3]}
            axisX={56}
            axisY={-9}
            type={PropertyType.DEPARTMENT}
            vertical
            parkingBottom
          />

          <PropertiesRow
            properties={departmentBlocks[4]}
            axisX={56}
            axisY={27}
            type={PropertyType.DEPARTMENT}
            vertical
            parkingBottom
          />

          <PropertiesRow
            properties={departmentBlocks[5]}
            axisX={-3}
            axisY={-9}
            type={PropertyType.DEPARTMENT}
            vertical
          />
        </g>

        {/* Estacionamiemto 1 a 6 */}
        <VisitsParkingSpotsRow
          parkingSpots={reversedParkingSpotsBlocks[0]}
          transformGroup="rotate(4,180,360)"
          axisX={517}
          axisY={290}
          writingMode="tb"
          textAnchor="end"
        />
        {/* Estacionamiemto 1 a 6 */}

        {/* Estacionamiemto 7 */}
        <VisitsParkingSpotsRow
          parkingSpots={reversedParkingSpotsBlocks[1]}
          transformGroup="rotate(-2,180,360)"
          axisX={360}
          axisY={310}
          textAnchor="start"
        />
        {/* Estacionamiemto 7 */}

        {/* Estacionamiemto 8*/}
        <VisitsParkingSpotsRow
          parkingSpots={reversedParkingSpotsBlocks[2]}
          transformGroup="rotate(-2,180,360)"
          axisX={263}
          axisY={310}
          textAnchor="end"
        />
        {/* Estacionamiemto 8 */}

        {/* Estacionamiemto 9 a 14 */}
        <VisitsParkingSpotsRow
          parkingSpots={reversedParkingSpotsBlocks[3]}
          transformGroup="rotate(-6,180,360)"
          axisX={53}
          axisY={323}
          writingMode="tb"
          textAnchor="start"
        />
        {/* Estacionamiemto 9 a 14 */}

        {/* Estacionamiento 15 */}
        <VisitsParkingSpotsRow
          parkingSpots={parkingSpotsBlocks[4]}
          axisX={299}
          axisY={152}
          writingMode="tb"
          textAnchor="end"
        />
        {/* Estacionamiento 15 */}

        {/* Estacionamiento 16 a 17 */}
        <VisitsParkingSpotsRow
          parkingSpots={reversedParkingSpotsBlocks[5]}
          axisX={297}
          axisY={85}
          writingMode="tb"
          textAnchor="end"
        />
        {/* Estacionamiento 16 a 17 */}

        {/* Estacionamiento 18 a 19 */}

        <VisitsParkingSpotsRow
          parkingSpots={reversedParkingSpotsBlocks[6]}
          axisX={279}
          axisY={85}
          writingMode="tb"
          textAnchor="end"
        />
        {/* Estacionamiento 18 a 19 */}

        {/* Estacionamiento 20 a 21 */}
        <VisitsParkingSpotsRow
          parkingSpots={reversedParkingSpotsBlocks[7]}
          axisX={504}
          axisY={17}
          vertical
          writingMode="tb"
          textAnchor="end"
        />
        {/* Estacionamiento 20 a 21 */}

        {/* Caseta */}
        <g>
          <circle cx="308" cy="340" r="7.5" fill="gray" />
          <rect x="300.5" y="340" width="15" height="15" fill="gray" />
        </g>
        {/* Caseta */}
      </svg>
    </div>
  );
};
