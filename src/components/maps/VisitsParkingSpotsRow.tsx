import { ParkingSpot } from "../../interfaces/parking-spot";

interface VisitsParkingSpotsRowProps {
  parkingSpots: ParkingSpot[];
  axisX: number;
  axisY: number;
  transformGroup?: string;
  vertical?: boolean;
  writingMode?: string;
  textAnchor?: string;
}

const handleParkingSpotClick = (parkingSpot: ParkingSpot["id"]) => {
  console.log(parkingSpot);
};

export const VisitsParkingSpotsRow: React.FC<VisitsParkingSpotsRowProps> = ({
  parkingSpots,
  axisX,
  axisY,
  transformGroup,
  vertical,
  writingMode,
  textAnchor,
}) => {
  return (
    <g transform={transformGroup}>
      {parkingSpots.map((parkingSpot, index) => (
        <g
          key={parkingSpot.id}
          onClick={() => handleParkingSpotClick(parkingSpot.id)}
          style={{
            cursor: "pointer",
          }}
        >
          <text
            x={vertical ? axisX : axisX + index * 7}
            y={vertical ? axisY + index * 18 : axisY}
            textAnchor={textAnchor}
            stroke={parkingSpot.isAvailable ? "green" : "red"}
            fontSize="5"
            writingMode={writingMode}
          >
            {parkingSpot.spotNumber}
          </text>
        </g>
      ))}
    </g>
  );
};
