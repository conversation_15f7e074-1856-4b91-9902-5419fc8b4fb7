import { Property, PropertyType } from "../../interfaces/property";

interface PropertiesRowProps {
  properties: Property[];
  axisX: number;
  axisY: number;
  transformGroup?: string;
  parkingBottom?: boolean;
  vertical?: boolean;
  type?: PropertyType;
}

const getParkingSpotPosition = (
  index: number,
  parkingBottom?: boolean,
  vertical?: boolean
) => {
  if (vertical) {
    return {
      x: parkingBottom ? 32 : 0,
      y: 3 + index * 10,
    };
  }
  return {
    x: 3 + index * 10,
    y: parkingBottom ? 32 : 0,
  };
};

export const PropertiesRow: React.FC<PropertiesRowProps> = ({
  properties,
  axisX,
  axisY,
  transformGroup,
  parkingBottom,
  vertical,
  type = PropertyType.HOUSE,
}) => {
  return type === PropertyType.HOUSE ? (
    <g transform={transformGroup ?? ""}>
      {properties.map((property, index) => {
        const translate = vertical
          ? `translate(${axisX},${axisY + index * 23})`
          : `translate(${axisX + index * 23},${axisY})`;
        return (
          <g
            key={property.id}
            onClick={() => console.log(property.id)}
            style={{ cursor: "pointer" }}
            transform={translate}
          >
            {/* House */}
            <rect
              width={vertical ? 40 : 21}
              height={vertical ? 21 : 40}
              stroke="#000"
              fill="#bec5c9"
              strokeWidth="1"
            />
            {/* Parking Spot */}
            {property.parkingSpots.map((parkingSpot, index) => {
              const parkingSpotPosition = getParkingSpotPosition(
                index,
                parkingBottom,
                vertical
              );

              return (
                // <text
                //   key={parkingSpot.id}
                //   x={parkingSpotPosition.x}
                //   y={parkingSpotPosition.y}
                //   fontSize={5}
                //   stroke={parkingSpot.isAvailable ? "green" : "#d37b7b"}
                // >
                //   {parkingSpot.spotNumber.split("-")[1]}
                // </text>
                <rect
                  key={parkingSpot.id}
                  x={parkingSpotPosition.x}
                  y={parkingSpotPosition.y}
                  width={vertical ? 8 : 5}
                  height={vertical ? 5 : 8}
                  fill={parkingSpot.isAvailable ? "green" : "red"}
                />
              );
            })}
            {/* Property number */}
            <text
              x={vertical ? 20 : 10}
              y={vertical ? 10 : 20}
              textAnchor="middle"
              fill="#000"
              fontSize="5"
              fontWeight="bold"
            >
              {property.address}
            </text>
          </g>
        );
      })}
    </g>
  ) : (
    <>
      {properties.map((property, index) => {
        const translate = vertical
          ? `translate(${axisX},${axisY + index * 12})`
          : `translate(${axisX + index * 12},${axisY})`;
        return (
          <g
            key={property.id}
            onClick={() => console.log(property)}
            style={{ cursor: "pointer" }}
            transform={translate}
          >
            {property.parkingSpots.map((parkingSpot, index) => (
              <text
                key={parkingSpot.id}
                x={13}
                y={5 + index * 5}
                // y={axisY + index * 5}
                textAnchor={parkingBottom ? "end" : "start"}
                fontSize="5"
                fontWeight="bold"
                fill={parkingSpot.isAvailable ? "green" : "red"}
              >
                {parkingSpot.spotNumber}
              </text>
            ))}
          </g>
        );
      })}
    </>
  );
};
