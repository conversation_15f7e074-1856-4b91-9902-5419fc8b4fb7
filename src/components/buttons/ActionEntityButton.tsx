import Button, { ButtonType } from "antd/es/button";
import { ReactNode } from "react";
import { useNavigate } from "@tanstack/react-router";
import { Flex, Tag } from "antd";

interface Entity {
  id: string;
}

interface ActionButtonProps<T extends Entity> {
  data: T | T[];
  getUrl: (entity: T) => string;
  getLabel: (entity: T) => string;
  icon?: (entity: T) => ReactNode;
  type?: ButtonType;
  emptyText?: string;
}

export const ActionEntityButton = <T extends Entity>({
  data,
  getUrl,
  getLabel,
  icon,
  type = "default",
  emptyText = "Sin datos",
}: ActionButtonProps<T>) => {
  const navigate = useNavigate();

  if (Array.isArray(data)) {
    if (data.length === 0) return <Tag color="gray">{emptyText}</Tag>;

    return (
      <Flex>
        {data.map((entity) => (
          <Button
            key={entity.id}
            onClick={() => navigate({ to: getUrl(entity) })}
            icon={icon ? icon(entity) : undefined}
            type={type}
          >
            {getLabel(entity)}
          </Button>
        ))}
      </Flex>
    );
  }

  return (
    <Button
      key={data.id}
      onClick={() => navigate({ to: getUrl(data) })}
      icon={icon ? icon(data) : undefined}
      type={type}
    >
      {getLabel(data)}
    </Button>
  );
};
