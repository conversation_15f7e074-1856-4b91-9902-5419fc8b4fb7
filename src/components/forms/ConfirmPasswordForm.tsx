import React from "react";
import { Form, Input, Button, Card, Space, Typography, message } from "antd";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import Title from "antd/es/typography/Title";
import { PasswordConfirmationValues } from "../../interfaces/auth";

const { Text } = Typography;

interface ConfirmPasswordFormProps {
  onSubmit: (data: PasswordConfirmationValues) => void;
}

export const ConfirmPasswordForm: React.FC<ConfirmPasswordFormProps> = ({
  onSubmit,
}) => {
  const onFinish = async (values: PasswordConfirmationValues) => {
    if (values.password !== values.passwordConfirmation) {
      message.error("Las contraseñas no coinciden");
    } else {
      onSubmit(values);
    }
  };

  return (
    <Card
      className="w-full max-w-md shadow-2xl"
      style={{
        background: "#ffff",
        backdropFilter: "blur(10px)",
        WebkitBackdropFilter: "blur(10px)",
      }}
    >
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        <Space direction="vertical" align="center" style={{ width: "100%" }}>
          <Title>Sabino</Title>
          <Text type="secondary">
            Crea una contraseña fuerte y única para tu cuenta
          </Text>
        </Space>
        <Form
          initialValues={{ remember: true }}
          name="password_form"
          onFinish={onFinish}
          layout="vertical"
        >
          <Form.Item
            name="password"
            label={
              <Text strong style={{ color: "#333" }}>
                Contraseña
              </Text>
            }
            rules={[
              {
                required: true,
                message: "¡Por favor, ingresa tu contraseña!",
              },
              {
                min: 8,
                message: "La contraseña debe tener al menos 8 caracteres",
              },
              {
                // pattern:
                //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                message:
                  "La contraseña debe contener al menos una letra mayúscula, una minúscula, un número y un carácter especial",
              },
            ]}
            hasFeedback
          >
            <Input.Password
              placeholder="Ingresa tu contraseña"
              iconRender={(visible) =>
                visible ? <EyeOutlined /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>

          <Form.Item
            name="passwordConfirmation"
            label={
              <Text strong style={{ color: "#333" }}>
                Confirmar Contraseña
              </Text>
            }
            dependencies={["password"]}
            rules={[
              {
                required: true,
                message: "¡Por favor, confirma tu contraseña!",
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error("¡Las contraseñas no coinciden!")
                  );
                },
              }),
            ]}
            hasFeedback
          >
            <Input.Password
              placeholder="Confirma tu contraseña"
              iconRender={(visible) =>
                visible ? <EyeOutlined /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              style={{
                width: "100%",
              }}
            >
              Establecer Contraseña
            </Button>
          </Form.Item>
        </Form>
      </Space>
    </Card>
  );
};
