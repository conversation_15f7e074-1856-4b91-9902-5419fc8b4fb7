import React from "react";
import { Form, Input, FormProps, Radio, Upload, Button, message } from "antd";
import { CloudUploadOutlined } from "@ant-design/icons";
import { RegulationType } from "../../interfaces/regulation";
import { useCustomRegulation } from "../../hooks/useCustomQueries";
import { FacilitySelect } from "../form-elements";
import { HiddenId, TitleInput } from "../form-elements/inputs";

export const RegulationForm: React.FC<
  FormProps & { handleCancel: () => void }
> = ({ form, handleCancel }) => {
  const { createMutation } = useCustomRegulation();
  const [fileList, setFileList] = React.useState<any[]>([]);

  const regulationTypes = Object.entries(RegulationType).map(
    ([key, value]) => ({
      value,
      label: key === RegulationType.AMENITY ? "Amenidad" : "General",
    })
  );

  const regulationType = Form.useWatch("type", form) || RegulationType.AMENITY;
  const facilityId = Form.useWatch("facilityId", form);

  const isEdit = form?.getFieldValue("id") !== undefined;

  const handleFileChange = (info: any) => {
    let newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const onFinish = async (values: any) => {
    const formData = new FormData();

    // Agregar los valores del formulario
    Object.keys(values).forEach((key) => {
      formData.append(key, values[key]);
    });

    // Agregar el archivo si existe
    if (fileList.length > 0) {
      formData.append("file", fileList[0].originFileObj);
    }

    // Ejecutar la mutación
    createMutation.mutate(formData, {
      onSuccess: () => {
        message.success("Reglamento creado exitosamente");
        form?.resetFields();
        setFileList([]);
        handleCancel();
      },
      onError: () => {
        message.error("Error al crear el reglamento");
        form?.resetFields();
        setFileList([]);
        handleCancel();
      },
    });
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        type: regulationType,
        facilityId: facilityId,
      }}
    >
      <HiddenId />

      <TitleInput />

      <Form.Item
        name="content"
        label="Contenido"
        rules={[
          {
            required: true,
            message: "Por favor ingresa un contenido",
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="type"
        label="Tipo de reglamento"
        rules={[
          { required: true, message: "Selecciona un tipo de reglamento" },
        ]}
      >
        <Radio.Group options={regulationTypes} />
      </Form.Item>

      {regulationType === RegulationType.AMENITY && <FacilitySelect />}

      {!isEdit && (
        <Form.Item label="Archivo del reglamento" name="file">
          <Upload
            beforeUpload={() => false} // No subir automáticamente
            accept=".pdf"
            maxCount={1}
            onChange={handleFileChange}
            fileList={fileList}
          >
            <Button icon={<CloudUploadOutlined />}>Agregar PDF</Button>
          </Upload>
        </Form.Item>
      )}
    </Form>
  );
};
