import React, { useEffect } from "react";
import { Form, FormProps, Input, UploadFile } from "antd";

import { HiddenId, TitleInput } from "../form-elements/inputs";
import { RoleSelect } from "../form-elements";
import { ImageUpload } from "../form-elements/ImageUpload";
import { mapImagesToUploadFile } from "../../utils.ts/form-utils";

export const AnnouncementForm: React.FC<FormProps> = ({ form }) => {
  const id = form?.getFieldValue("id");
  const isEdit = id !== undefined;
  const watchedImages = Form.useWatch("images", form);

  useEffect(() => {
    if (!isEdit) return;
    const images = form?.getFieldValue("images");
    if (images && Array.isArray(images)) {
      const uploadedImages = mapImagesToUploadFile(images);
      form?.setFieldValue("images", uploadedImages);
    }
  }, [isEdit, form]);

  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <TitleInput />
      <Form.Item
        name="message"
        label="Mensaje"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un mensaje!",
          },
        ]}
      >
        <Input.TextArea rows={6} maxLength={300} />
      </Form.Item>
      <RoleSelect />
      <Form.Item
        label="Imágenes"
        name="images"
        rules={[
          {
            validator: (_, value) => {
              if (value && value.length > 5) {
                return Promise.reject(
                  new Error("Solo se pueden subir hasta 5 imágenes")
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <ImageUpload
          value={watchedImages}
          onChange={(files: UploadFile[]) =>
            form?.setFieldValue("images", files)
          }
          maxFiles={1}
        />
      </Form.Item>
    </Form>
  );
};
