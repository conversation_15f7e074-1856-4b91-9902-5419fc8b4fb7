import { PlusOutlined, MinusCircleOutlined } from "@ant-design/icons";
import { Button, Form, FormProps, Input, InputNumber, Space } from "antd";
import {
  DescriptionInput,
  HiddenId,
  TitleInput,
} from "../form-elements/inputs";
import { FacilitySelect } from "../form-elements";

export const ProtocolForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form
      form={form}
      name="protocol_steps"
      autoComplete="off"
      initialValues={{ steps: [] }}
    >
      <HiddenId />
      <TitleInput />
      <DescriptionInput />
      <FacilitySelect />
      <Form.List name="steps">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Space
                key={key}
                style={{ display: "flex", marginBottom: 8 }}
                align="baseline"
              >
                <Form.Item
                  {...restField}
                  name={[name, "order"]}
                  rules={[{ required: true, message: "Ingresa un número" }]}
                >
                  <InputNumber placeholder="1" min={1} controls={false} />
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, "title"]}
                  rules={[{ required: true, message: "Ingresa un texto" }]}
                >
                  <Input placeholder="Tomar foro a placa" />
                </Form.Item>
                <MinusCircleOutlined onClick={() => remove(name)} />
              </Space>
            ))}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => add()}
                block
                icon={<PlusOutlined />}
              >
                Add field
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </Form>
  );
};
