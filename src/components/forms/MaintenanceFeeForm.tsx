import React from "react";
import { Form, FormProps, InputNumber } from "antd";

import { HiddenId } from "../form-elements/inputs";

export const MaintenanceFeeForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <Form.Item
        name="amount"
        label="Monto"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un monto!",
          },
        ]}
      >
        <InputNumber min={0} max={4} controls={false} />
      </Form.Item>
      <Form.Item
        name="year"
        label="Año"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un año!",
          },
        ]}
      >
        <InputNumber min={0} max={4} controls={false} />
      </Form.Item>
    </Form>
  );
};
