import React, { useEffect } from "react";
import { Form, Input, FormProps } from "antd";
import { UploadFile } from "antd/es/upload/interface";
import { PropertySelect, StatusSelect, UserSelect } from "../form-elements";
import { Status } from "../../interfaces/maintenance-issue-report";
import { HiddenId } from "../form-elements/inputs";
import { ImageUpload } from "../form-elements/ImageUpload";
import { mapImagesToUploadFile } from "../../utils.ts/form-utils";

export const MaintenanceIssueReportForm: React.FC<FormProps> = ({ form }) => {
  const id = form?.getFieldValue("id");
  const isEdit = id !== undefined;
  const status = Form.useWatch("status", form) ?? Status.OPEN;
  const watchedImages = Form.useWatch("images", form);

  useEffect(() => {
    if (!isEdit) return;
    const images = form?.getFieldValue("images");
    if (images && Array.isArray(images)) {
      const uploadedImages = mapImagesToUploadFile(images);
      form?.setFieldValue("images", uploadedImages);
    }
  }, [isEdit, form]);

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        status: status,
      }}
    >
      <HiddenId />
      <UserSelect name="reportedBy" label="Reportado por" />
      <PropertySelect />
      <StatusSelect disabled={!isEdit} />

      <Form.Item
        label="Descripción del problema"
        name="description"
        rules={[{ required: true, message: "Este campo es requerido" }]}
      >
        <Input.TextArea rows={4} />
      </Form.Item>

      <Form.Item
        label="Imágenes"
        name="images"
        rules={[
          {
            validator: (_, value) => {
              if (value && value.length > 5) {
                return Promise.reject(
                  new Error("Solo se pueden subir hasta 5 imágenes")
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <ImageUpload
          value={watchedImages}
          onChange={(files: UploadFile[]) =>
            form?.setFieldValue("images", files)
          }
          maxFiles={3}
        />
      </Form.Item>
    </Form>
  );
};
