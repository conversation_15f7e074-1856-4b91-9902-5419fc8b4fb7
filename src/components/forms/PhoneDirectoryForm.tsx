import { Form, FormProps, Input } from "antd";
import { HiddenId } from "../form-elements/inputs";

export const PhoneDirectoryForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <Form.Item
        name="name"
        label="Nombre"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un nombre!",
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="phoneNumber"
        label="Número"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un número!",
          },
        ]}
      >
        <Input />
      </Form.Item>
    </Form>
  );
};
