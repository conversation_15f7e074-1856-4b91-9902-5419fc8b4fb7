import React from "react";
import { Form, FormProps, DatePicker, Input } from "antd";

import { shortDateFormat } from "../../utils.ts/time-utils";

import {
  HiddenId,
  TitleInput,
  DescriptionInput,
} from "../form-elements/inputs";
import { RolesSelect } from "../form-elements";

export const EventForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <TitleInput />
      <DescriptionInput />
      <RolesSelect />
      <Form.Item
        name="location"
        label="Ubicación"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa una ubicación!",
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Inicio"
        name="startDate"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa una fecha de inicio!",
          },
        ]}
      >
        <DatePicker disabled={false} showTime format={shortDateFormat} />
      </Form.Item>

      <Form.Item
        label="Final"
        name="endDate"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa una fecha de final!",
          },
        ]}
      >
        <DatePicker disabled={false} showTime format={shortDateFormat} />
      </Form.Item>
    </Form>
  );
};
