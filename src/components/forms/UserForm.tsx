import { Form, FormProps, Select } from "antd";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Property } from "../../interfaces/property";
import { BasicUserInfoInputs, HiddenId } from "../form-elements/inputs";
import { useEffect } from "react";
import { User } from "../../interfaces/user";
import { RolesSelect } from "../form-elements";

interface UserFormProps extends FormProps {
  user?: any;
}

export const UserForm: React.FC<UserFormProps> = ({ form, user }) => {
  const { data: propertiesCached } = useCachedQuery<Property>(
    "property/for-select"
  );
  const properties = propertiesCached || [];

  const normalizeUserForm = (user: User) => {
    return {
      ...user,
      roles: user.roles?.map((r) => r.id),
      properties: user.properties?.map((p) => p.id),
    };
  };

  useEffect(() => {
    if (user) {
      form?.setFieldsValue(normalizeUserForm(user));
    }
  }, [user, form]);

  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <BasicUserInfoInputs />

      <RolesSelect />

      <Form.Item
        name="properties"
        label="Propiedad"
        rules={[
          {
            required: true,
            message: "Por favor selecciona una propiedad",
          },
        ]}
      >
        <Select
          mode="multiple"
          optionFilterProp="label"
          showSearch
          filterSort={(optionA, optionB) =>
            (optionA?.label ?? "")
              .toLowerCase()
              .localeCompare((optionB?.label ?? "").toLowerCase())
          }
          style={{ width: 120 }}
          options={properties.map((property) => {
            return {
              value: property.id,
              label: property.address,
            };
          })}
        />
      </Form.Item>
    </Form>
  );
};
