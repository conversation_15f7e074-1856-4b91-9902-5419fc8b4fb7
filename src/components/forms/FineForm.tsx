import { useEffect } from "react";
import { Form, FormProps, InputNumber, UploadFile } from "antd";

import { PropertySelect } from "../form-elements";
import { DescriptionInput, HiddenId } from "../form-elements/inputs";
import { mapImagesToUploadFile } from "../../utils.ts/form-utils";
import { ImageUpload } from "../form-elements/ImageUpload";

export const FineForm: React.FC<FormProps> = ({ form }) => {
  const id = form?.getFieldValue("id");
  const isEdit = id !== undefined;
  const watchedImages = Form.useWatch("images", form);

  useEffect(() => {
    if (!isEdit) return;
    const images = form?.getFieldValue("images");
    if (images && Array.isArray(images)) {
      const uploadedImages = mapImagesToUploadFile(images);
      form?.setFieldValue("images", uploadedImages);
    }
  }, [isEdit, form]);

  return (
    <Form form={form} layout="vertical" initialValues={{ amount: 0 }}>
      <HiddenId />
      <Form.Item
        name="amount"
        label="Monto"
        normalize={(value) =>
          typeof value === "string" ? Number(value) : value
        }
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un monto!",
          },
        ]}
      >
        <InputNumber
          min={0}
          controls={false}
          onKeyUp={(event: any) => {
            if (!/\d/.test(event.key)) {
              event.preventDefault();
            }
          }}
        />
      </Form.Item>
      <DescriptionInput />
      <PropertySelect />

      <Form.Item
        label="Imágenes"
        name="images"
        rules={[
          {
            validator: (_, value) => {
              if (value && value.length > 5) {
                return Promise.reject(
                  new Error("Solo se pueden subir hasta 5 imágenes")
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <ImageUpload
          value={watchedImages}
          onChange={(files: UploadFile[]) =>
            form?.setFieldValue("images", files)
          }
          maxFiles={3}
        />
      </Form.Item>
    </Form>
  );
};
