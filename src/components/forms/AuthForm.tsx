import React from "react";
import { Form, Input, Button } from "antd";
import { AuthRequest } from "../../interfaces/auth";

interface AuthFormProps {
  onSubmit: (data: AuthRequest) => void;
}

export const AuthForm: React.FC<AuthFormProps> = ({ onSubmit }) => {
  const onFinish = async (values: AuthRequest) => {
    onSubmit(values);
  };

  return (
    <Form
      name="basic"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{ remember: true }}
    >
      <Form.Item
        label="Email"
        name="email"
        rules={[
          { required: true, message: "Please input your email!" },
          { type: "email", message: "Please enter a valid email!" },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Password"
        name="password"
        rules={[{ required: true, message: "Please input your password!" }]}
      >
        <Input.Password />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          Login
        </Button>
      </Form.Item>
    </Form>
  );
};
