import { Form, FormProps, Input } from "antd";
import { PropertySelect } from "../form-elements";
import { HiddenId } from "../form-elements/inputs";

export const VehicleForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <Form.Item
        name="plate"
        label="Placa"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa una placa!",
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="brand"
        label="Marca"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa una marca!",
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="model"
        label="Modelo"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un modelo!",
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="color"
        label="Color"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un color!",
          },
        ]}
      >
        <Input />
      </Form.Item>

      <PropertySelect />
    </Form>
  );
};
