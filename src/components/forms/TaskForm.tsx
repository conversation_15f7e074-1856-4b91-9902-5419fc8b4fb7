import React from "react";
import { Form, FormProps, DatePicker, Select } from "antd";
import { shortDateFormat } from "../../utils.ts/time-utils";
import { TaskStatusSelect } from "../form-elements";
import {
  HiddenId,
  TitleInput,
  DescriptionInput,
} from "../form-elements/inputs";
import { TaskStatus } from "../../interfaces/task";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { User } from "../../interfaces/user";

export const TaskForm: React.FC<FormProps> = ({ form }) => {
  const isEdit = form?.getFieldValue("id") !== undefined;
  const status = Form.useWatch("status", form) ?? TaskStatus.PENDING;
  const { data: usersCached } = useCachedQuery<User>("user/for-select");
  const users = usersCached || [];

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        status: status,
      }}
    >
      <HiddenId />

      <TitleInput />

      <DescriptionInput />

      <TaskStatusSelect disabled={false} />

      <Form.Item
        label="Fecha límite"
        name="dueDate"
        rules={[
          {
            required: true,
            message: "¡Por favor selecciona una fecha límite!",
          },
        ]}
      >
        <DatePicker disabled={false} showTime format={shortDateFormat} />
      </Form.Item>

      <Form.Item name="userId" label="Asignado a">
        <Select
          optionFilterProp="label"
          showSearch
          allowClear
          placeholder="Seleccionar usuario (opcional)"
          filterSort={(optionA, optionB) =>
            (optionA?.label ?? "")
              .toLowerCase()
              .localeCompare((optionB?.label ?? "").toLowerCase())
          }
          style={{ width: "100%" }}
          options={users.map((user) => {
            return {
              value: user.id,
              label: `${user.firstName} ${user.paternalLastName} ${user.maternalLastName}`,
            };
          })}
        />
      </Form.Item>

      {isEdit && status === TaskStatus.COMPLETED && (
        <Form.Item label="Fecha de completado" name="completedAt">
          <DatePicker disabled={true} showTime format={shortDateFormat} />
        </Form.Item>
      )}
    </Form>
  );
};
