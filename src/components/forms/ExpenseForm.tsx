import React from "react";
import { Form, FormProps, DatePicker, InputNumber, Input } from "antd";
import { shortDateFormat } from "../../utils.ts/time-utils";
import { ServiceSelect, SupplierSelect } from "../form-elements";
import { HiddenId, DescriptionInput } from "../form-elements/inputs";

export const ExpenseForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      
      <DescriptionInput />

      <Form.Item
        name="amount"
        label="Monto"
        normalize={(value) =>
          typeof value === "string" ? Number(value) : value
        }
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un monto!",
          },
        ]}
      >
        <InputNumber
          min={0}
          controls={false}
          style={{ width: "100%" }}
          placeholder="0.00"
          formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
        />
      </Form.Item>

      <Form.Item
        label="Fecha del gasto"
        name="date"
        rules={[
          {
            required: true,
            message: "¡Por favor selecciona una fecha!",
          },
        ]}
      >
        <DatePicker disabled={false} format={shortDateFormat} style={{ width: "100%" }} />
      </Form.Item>

      <Form.Item
        name="receipt"
        label="Recibo / Factura"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa el número de recibo o factura!",
          },
        ]}
      >
        <Input placeholder="Número de recibo o factura" />
      </Form.Item>

      <ServiceSelect required={false} />
      
      <SupplierSelect required={false} />
    </Form>
  );
};
