import { useEffect } from "react";
import { Form, FormProps, Input, UploadFile } from "antd";
import { HiddenId } from "../form-elements/inputs";
import { PackageStatus } from "../../interfaces/packages";
import { mapImagesToUploadFile } from "../../utils.ts/form-utils";
import { ImageUpload } from "../form-elements/ImageUpload";
import { NumberSelect, PropertySelect } from "../form-elements";

export const PackageForm: React.FC<FormProps> = ({ form }) => {
  const id = form?.getFieldValue("id");
  const isEdit = form?.getFieldValue("id") !== undefined;
  const status = Form.useWatch("status", form) ?? PackageStatus.PENDING;
  const watchedImages = Form.useWatch("images", form);

  useEffect(() => {
    if (!isEdit) return;
    const images = form?.getFieldValue("images");
    if (images && Array.isArray(images)) {
      const uploadedImages = mapImagesToUploadFile(images);
      form?.setFieldValue("images", uploadedImages);
    }
  }, [isEdit, form]);

  return (
    <Form form={form} layout="vertical" initialValues={{ status: status }}>
      <HiddenId />
      <PropertySelect />
      {/* Form item for field number type number and max 5 */}
      <NumberSelect
        name="number"
        label="Número de seguimiento"
        max={5}
        required={true}
      />
      <Form.Item
        label="Notas"
        name="notes"
        rules={[{ required: false, message: "Este campo es requerido" }]}
      >
        <Input.TextArea
          rows={3}
          maxLength={300}
          placeholder="Notas del paquete"
        />
      </Form.Item>
      <Form.Item
        label="Imágenes"
        name="images"
        rules={[
          {
            validator: (_, value) => {
              if (value && value.length > 5) {
                return Promise.reject(
                  new Error("Solo se pueden subir hasta 5 imágenes")
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <ImageUpload
          value={watchedImages}
          onChange={(files: UploadFile[]) =>
            form?.setFieldValue("images", files)
          }
          maxFiles={3}
        />
      </Form.Item>
    </Form>
  );
};
