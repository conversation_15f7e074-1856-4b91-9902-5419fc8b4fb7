import React, { useEffect } from "react";
import { Form, Input, FormProps, DatePicker, UploadFile } from "antd";

import { longDateTimeFormat } from "../../utils.ts/time-utils";
import {
  ComplainTypeSelect,
  PrioritySelect,
  PropertySelect,
  StatusSelect,
  UserSelect,
} from "../form-elements";
import { Status } from "../../interfaces/maintenance-issue-report";
import { HiddenId } from "../form-elements/inputs";
import { ImageUpload } from "../form-elements/ImageUpload";
import { mapImagesToUploadFile } from "../../utils.ts/form-utils";

export const ComplaintForm: React.FC<FormProps> = ({ form }) => {
  const isEdit = form?.getFieldValue("id") !== undefined;
  const status = Form.useWatch("status", form) ?? Status.OPEN;
  const watchedImages = Form.useWatch("images", form);

  useEffect(() => {
    if (!isEdit) return;
    const images = form?.getFieldValue("images");
    if (images && Array.isArray(images)) {
      const uploadedImages = mapImagesToUploadFile(images);
      form?.setFieldValue("images", uploadedImages);
    }
  }, [isEdit, form]);

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        staus: status,
      }}
    >
      <HiddenId />
      <ComplainTypeSelect />
      <UserSelect />
      <PropertySelect />
      <StatusSelect disabled={isEdit} />

      <PrioritySelect disabled={isEdit} />

      <Form.Item
        label="Detalle de la queja"
        name="detail"
        rules={[{ required: true, message: "Este campo es requerido" }]}
      >
        <Input />
      </Form.Item>

      <Form.Item label="completedAt" name="Fecha de cierre">
        <DatePicker disabled={!isEdit} showTime format={longDateTimeFormat} />
      </Form.Item>

      <Form.Item
        label="Imágenes"
        name="images"
        rules={[
          {
            validator: (_, value) => {
              if (value && value.length > 5) {
                return Promise.reject(
                  new Error("Solo se pueden subir hasta 5 imágenes")
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <ImageUpload
          value={watchedImages}
          onChange={(files: UploadFile[]) =>
            form?.setFieldValue("images", files)
          }
          maxFiles={3}
        />
      </Form.Item>
    </Form>
  );
};
