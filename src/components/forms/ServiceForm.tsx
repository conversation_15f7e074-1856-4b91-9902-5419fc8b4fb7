import { Form, FormProps, InputNumber } from "antd";
import { DescriptionInput, HiddenId, NameInput } from "../form-elements/inputs";
import { SupplierSelect } from "../form-elements";

export const ServiceForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <NameInput />
      <DescriptionInput />
      <SupplierSelect />
      <Form.Item
        name="cost"
        label="Costo"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un costo!",
          },
        ]}
      >
        <InputNumber min={0} controls={false} />
      </Form.Item>
    </Form>
  );
};
