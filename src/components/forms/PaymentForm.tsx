import React from "react";
import { Form, FormProps, DatePicker, InputNumber, Input } from "antd";
import { shortDateFormat } from "../../utils.ts/time-utils";
import { PropertySelect } from "../form-elements";
import { HiddenId } from "../form-elements/inputs";

export const PaymentForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      
      <Form.Item
        name="amount"
        label="Monto"
        normalize={(value) =>
          typeof value === "string" ? Number(value) : value
        }
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un monto!",
          },
        ]}
      >
        <InputNumber
          min={0}
          controls={false}
          style={{ width: "100%" }}
          placeholder="0.00"
          formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
        />
      </Form.Item>

      <Form.Item
        label="Fecha de pago"
        name="paymentDate"
        rules={[
          {
            required: true,
            message: "¡Por favor selecciona una fecha de pago!",
          },
        ]}
      >
        <DatePicker disabled={false} format={shortDateFormat} style={{ width: "100%" }} />
      </Form.Item>

      <Form.Item
        name="description"
        label="Descripción"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa una descripción!",
          },
        ]}
      >
        <Input.TextArea rows={3} maxLength={300} placeholder="Descripción del pago" />
      </Form.Item>

      <PropertySelect />
    </Form>
  );
};
