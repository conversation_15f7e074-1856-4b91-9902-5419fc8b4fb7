import { Form, FormProps, Input } from "antd";
import { HiddenId } from "../form-elements/inputs";

export const PackageTokenForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <Form.Item
        name="deliveryToken"
        label="Token de entrega"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un token!",
          },
        ]}
      >
        <Input />
      </Form.Item>
    </Form>
  );
};
