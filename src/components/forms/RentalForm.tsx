import React from "react";
import { Form, FormProps, DatePicker, InputNumber } from "antd";

import { shortDateFormat } from "../../utils.ts/time-utils";
import { PropertySelect, RentalStatusSelect } from "../form-elements";
import { HiddenId } from "../form-elements/inputs";

export const RentalForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />

      <Form.Item label="Inicio" name="startDate">
        <DatePicker disabled={false} format={shortDateFormat} />
      </Form.Item>

      <Form.Item label="Final" name="endtDate">
        <DatePicker disabled={false} format={shortDateFormat} />
      </Form.Item>

      <Form.Item
        name="monthlyRate"
        label="Monto"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa un monto!",
          },
        ]}
      >
        <InputNumber min={0} controls={false} />
      </Form.Item>
      <RentalStatusSelect />
      <PropertySelect />
    </Form>
  );
};
