import { DatePicker, Form, FormProps, Input } from "antd";
import { BasicUserInfoInputs, HiddenId } from "../form-elements/inputs";
import { SupplierSelect } from "../form-elements";
import { shortDateFormat } from "../../utils.ts/time-utils";

export const EmployeeForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <BasicUserInfoInputs />
      <Form.Item
        name="position"
        label="Cargo"
        rules={[
          {
            required: true,
            message: "Por favor ingresa tu cargo",
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item label="date" name="hireDate">
        <DatePicker format={shortDateFormat} />
      </Form.Item>
      <SupplierSelect />
    </Form>
  );
};
