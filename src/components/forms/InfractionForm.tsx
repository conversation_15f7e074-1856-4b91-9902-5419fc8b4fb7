import React, { useEffect } from "react";
import { Form, FormProps, DatePicker, UploadFile } from "antd";

import { shortDateFormat } from "../../utils.ts/time-utils";
import { InfractionSeveritySelect, PropertySelect } from "../form-elements";
import { Status } from "../../interfaces/maintenance-issue-report";
import { DescriptionInput, HiddenId } from "../form-elements/inputs";
import { mapImagesToUploadFile } from "../../utils.ts/form-utils";
import { ImageUpload } from "../form-elements/ImageUpload";

export const InfractionForm: React.FC<FormProps> = ({ form }) => {
  const id = form?.getFieldValue("id");
  const isEdit = id !== undefined;
  const status = Form.useWatch("status", form) ?? Status.OPEN;
  const watchedImages = Form.useWatch("images", form);

  useEffect(() => {
    if (!isEdit) return;
    const images = form?.getFieldValue("images");
    if (images && Array.isArray(images)) {
      const uploadedImages = mapImagesToUploadFile(images);
      form?.setFieldValue("images", uploadedImages);
    }
  }, [isEdit, form]);

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        staus: status,
      }}
    >
      <HiddenId />
      <DescriptionInput />
      <PropertySelect />
      <InfractionSeveritySelect disabled={false} />

      <Form.Item name="date" label="Fecha de acontecimiento">
        <DatePicker disabled={false} showTime format={shortDateFormat} />
      </Form.Item>

      <Form.Item
        label="Imágenes"
        name="images"
        rules={[
          {
            validator: (_, value) => {
              if (value && value.length > 5) {
                return Promise.reject(
                  new Error("Solo se pueden subir hasta 5 imágenes")
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <ImageUpload
          value={watchedImages}
          onChange={(files: UploadFile[]) =>
            form?.setFieldValue("images", files)
          }
          maxFiles={3}
        />
      </Form.Item>
    </Form>
  );
};
