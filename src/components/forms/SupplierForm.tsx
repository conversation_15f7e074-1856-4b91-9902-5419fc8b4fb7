import { Form, FormProps, Input } from "antd";
import { HiddenId, NameInput } from "../form-elements/inputs";
import { MailOutlined } from "@ant-design/icons";

export const SupplierForm: React.FC<FormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <HiddenId />
      <NameInput />
      <Form.Item
        name="phone"
        label="Teléfono"
        rules={[
          {
            required: true,
            message: "Por favor ingresa tu número telefónico",
          },
        ]}
      >
        <Input prefix={<MailOutlined />} placeholder="4425111115" />
      </Form.Item>
      <Form.Item
        name="email"
        label="Correo Electrónico"
        rules={[
          {
            required: true,
            message: "Por favor ingresa tu correo electrónico",
          },
          {
            type: "email",
            message: "Por favor ingresa un correo electrónico válido",
          },
        ]}
      >
        <Input prefix={<MailOutlined />} placeholder="<EMAIL>" />
      </Form.Item>
      <Form.Item
        name="address"
        label="Dirección"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa una dirección!",
          },
        ]}
      >
        <Input max={50} />
      </Form.Item>
    </Form>
  );
};
