import React from "react";
import { Form, FormProps, DatePicker, InputNumber } from "antd";

import { shortDateFormat, todayDate } from "../../utils.ts/time-utils";
import { FacilitySelect, PropertySelect, UserSelect } from "../form-elements";
import { HiddenId } from "../form-elements/inputs";

export const ReservationForm: React.FC<FormProps> = ({ form }) => {
  const date = Form.useWatch("date", form) ?? todayDate();

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        date: date,
      }}
    >
      <HiddenId />
      <UserSelect label="Solicitado por" name="requestedBy" />
      <Form.Item
        name="amountOfPeople"
        label="# asistentes"
        rules={[
          {
            required: true,
            message: "¡Por favor ingresa número de asistentes!",
          },
        ]}
      >
        <InputNumber min={0} controls={false} max={10} />
      </Form.Item>
      {/* status falta */}
      <PropertySelect />
      <FacilitySelect />

      <Form.Item label="Fecha de inicio" name="startDateTime">
        <DatePicker disabled={false} showTime format={shortDateFormat} />
      </Form.Item>
      <Form.Item label="Fecha de final" name="endDateTime">
        <DatePicker disabled={false} showTime format={shortDateFormat} />
      </Form.Item>
    </Form>
  );
};
