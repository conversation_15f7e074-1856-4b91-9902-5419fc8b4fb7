import React from "react";
import { Form, Input, FormProps, Select, DatePicker, Radio } from "antd";
import { useParkingSpots } from "../../hooks/useCustomQueries";
import {
  dateFormat,
  longDateTimeFormat,
  todayDate,
} from "../../utils.ts/time-utils";
import { VisitMethod } from "../../interfaces/visit";
import { ParkingSpotType } from "../../interfaces/parking-spot";
import { HiddenId } from "../form-elements/inputs";
import { PropertySelect, UserSelect } from "../form-elements";

export const VisitForm: React.FC<FormProps> = ({ form }) => {
  const { visitorParkingSpotsQuery, propertyParkingSpotsQuery } =
    useParkingSpots();
  const parkingSpotId = form?.getFieldValue("parkingSpotId");
  const propertyId = Form.useWatch("propertyId", form);
  const visitMethodOptions = Object.entries(VisitMethod).map(
    ([key, value]) => ({
      value,
      label: key === VisitMethod.WALKING ? "A Pie" : "Auto",
    })
  );
  const parkingOptions = Object.entries(ParkingSpotType).map(
    ([key, value]) => ({
      value,
      label: key === ParkingSpotType.RESIDENT ? "Propiedad" : "Visitas",
    })
  );
  const visitMethod = Form.useWatch("visitMethod", form) || "CAR";
  const type = Form.useWatch("type", form) || ParkingSpotType.VISITOR;

  const { data: propertyParkingSpotsData } =
    propertyParkingSpotsQuery(propertyId);

  const visitParkingSpots =
    visitorParkingSpotsQuery.data
      ?.filter(
        (parkingSpot) =>
          parkingSpot.isAvailable === true || parkingSpot.id === parkingSpotId
      )
      .map((parkingSpot) => {
        return {
          value: parkingSpot.id,
          label: parkingSpot.spotNumber,
        };
      }) || [];

  const propertyParkingSpots =
    propertyParkingSpotsData
      ?.filter(
        (parkingSpot) =>
          parkingSpot.isAvailable === true || parkingSpot.id === parkingSpotId
      )
      .map((parkingSpot) => {
        return {
          value: parkingSpot.id,
          label: parkingSpot.spotNumber,
        };
      }) || [];

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        schedule: todayDate(),
        checkInTime: todayDate(),
        visitMethod: visitMethod,
        type: type,
      }}
    >
      <HiddenId />
      <PropertySelect />
      <UserSelect label="Solicitado por" name="requestedBy" />

      {/* Visitor Name */}
      <Form.Item
        label="Visitor Name"
        name="visitorName"
        rules={[{ required: true, message: "Visitor Name is required" }]}
      >
        <Input />
      </Form.Item>

      {/* Método de Ingreso */}
      <Form.Item
        name="visitMethod"
        label="Método de Ingreso"
        rules={[{ required: true, message: "Selecciona un método de ingreso" }]}
      >
        <Radio.Group options={visitMethodOptions} />
      </Form.Item>

      {visitMethod === VisitMethod.CAR && (
        <>
          <Form.Item
            label="Vehicle Plate"
            name="vehiclePlate"
            rules={[
              {
                required: visitMethod === "car",
                message: "La placa es obligatoria si ingresas en auto",
              },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="type"
            label="Tipo de estacionamiento"
            rules={[
              {
                required: true,
                message: "Selecciona un tipo de estacionamiento",
              },
            ]}
          >
            <Radio.Group options={parkingOptions} />
          </Form.Item>

          {type === ParkingSpotType.VISITOR ? (
            <Form.Item label="Lugar de estacionamiento" name="parkingSpotId">
              <Select
                optionFilterProp="label"
                showSearch
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                style={{ width: 120 }}
                options={visitParkingSpots}
              />
            </Form.Item>
          ) : (
            <Form.Item label="Lugar de estacionamiento" name="parkingSpotId">
              <Select
                optionFilterProp="label"
                showSearch
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                style={{ width: 120 }}
                options={propertyParkingSpots}
              />
            </Form.Item>
          )}
        </>
      )}

      {/* Visit Date (NO editable, solo fecha actual) */}
      <Form.Item label="Visita programada" name="schedule">
        <DatePicker disabled showTime format={dateFormat} />
      </Form.Item>

      {/* Check-in Time (NO editable, fecha y hora actual) */}
      <Form.Item label="Check-in" name="checkInTime">
        <DatePicker disabled showTime format={longDateTimeFormat} />
      </Form.Item>

      {/* Check-in Time (NO editable, fecha y hora actual) */}
      <Form.Item label="Check-out" name="checkOutTime">
        <DatePicker disabled showTime format={longDateTimeFormat} />
      </Form.Item>
    </Form>
  );
};
