import {
  Button,
  Checkbox,
  DatePicker,
  Flex,
  Form,
  FormProps,
  InputNumber,
  message,
  Upload,
} from "antd";
import { DescriptionInput, HiddenId, NameInput } from "../form-elements/inputs";
import { useEffect, useState } from "react";
import { CloudUploadOutlined } from "@ant-design/icons";
import { useCustomFacitlity } from "../../hooks/useCustomQueries";
import { daysOfWeekOptions } from "../../utils.ts/time-utils";

export const FacilityForm: React.FC<
  FormProps & { handleCancel: () => void }
> = ({ form, handleCancel }) => {
  const reservable = Form.useWatch("reservable", form);
  const [isReservable, setIsReservable] = useState<boolean>(!!reservable);
  const [fileList, setFileList] = useState<any[]>([]);
  const { createMutation, updateMutation } = useCustomFacitlity();

  const handleFileChange = (info: any) => {
    let newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const onFinish = async (values: any) => {
    const id: string = form?.getFieldValue("id");
    const isEditing = id !== undefined;

    const formData = new FormData();

    // Agregar los valores del formulario
    Object.keys(values).forEach((key) => {
      formData.append(key, values[key]);
    });

    // Agregar el archivo si existe
    if (fileList.length > 0) {
      formData.append("file", fileList[0].originFileObj);
    }

    if (!isEditing) {
      // Ejecutar la mutación
      createMutation.mutate(formData, {
        onSuccess: () => {
          message.success("Reglamento creado exitosamente");
          form?.resetFields();
          setFileList([]);
          handleCancel();
        },
        onError: () => {
          message.error("Error al crear el reglamento");
          form?.resetFields();
          setFileList([]);
          handleCancel();
        },
      });
    } else {
      updateMutation.mutate(
        { id, data: formData },
        {
          onSuccess: () => {
            message.success("Reglamento actualizado exitosamente");
            form?.resetFields();
            setFileList([]);
            handleCancel();
          },
          onError: () => {
            message.error("Error al actualizar el reglamento");
            form?.resetFields();
            setFileList([]);
            handleCancel();
          },
        }
      );
    }
  };

  useEffect(() => {
    setIsReservable(!!reservable);
  }, [reservable]);

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <HiddenId />
      <NameInput />
      <Form.Item label="Imagen" name="file">
        <Upload
          beforeUpload={() => false}
          accept=".jpg,.jpeg,.png"
          maxCount={1}
          onChange={handleFileChange}
          fileList={fileList}
        >
          <Button icon={<CloudUploadOutlined />}>Agregar imagen</Button>
        </Upload>
      </Form.Item>
      <DescriptionInput />

      <Flex gap={30}>
        <Form.Item
          label="Abre"
          name="open"
          rules={[
            {
              required: true,
              message: "¡Por favor ingresa la hora de apertura!",
            },
          ]}
        >
          <DatePicker picker="time" />
        </Form.Item>

        <Form.Item
          label="Cierra"
          name="close"
          rules={[
            {
              required: true,
              message: "¡Por favor ingresa la hora de cierre!",
            },
          ]}
        >
          <DatePicker picker="time" />
        </Form.Item>
      </Flex>

      <Form.Item name="reservable" valuePropName="checked">
        <Checkbox onChange={(e) => setIsReservable(e.target.checked)}>
          Reservable
        </Checkbox>
      </Form.Item>

      {isReservable && (
        <>
          <Flex gap={30}>
            <Form.Item
              label="Inicio reserva"
              name="startTime"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const reservable = getFieldValue("reservable");
                    if (reservable && !value) {
                      return Promise.reject(
                        new Error("¡Ingresa la hora de inicio de reserva!")
                      );
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <DatePicker picker="time" />
            </Form.Item>

            <Form.Item
              label="Final reserva"
              name="endTime"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const reservable = getFieldValue("reservable");
                    if (reservable && !value) {
                      return Promise.reject(
                        new Error("¡Ingresa la hora de fin de reserva!")
                      );
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <DatePicker picker="time" />
            </Form.Item>
          </Flex>

          <Form.Item
            label="Máximo de personas"
            name="maxAmountOfPeople"
            rules={[
              {
                required: true,
                message: "¡Por favor ingresa el máximo de personas!",
              },
            ]}
          >
            <InputNumber min={0} controls={false} />
          </Form.Item>
          <Form.Item
            label="Tiempo máximo de uso (en horas)"
            name="maxTimeOfStay"
            rules={[
              {
                required: true,
                message:
                  "¡Por favor ingresa el Tiempo máximo de uso (en horas)!",
              },
            ]}
          >
            <InputNumber min={0} controls={false} />
          </Form.Item>

          <Form.Item
            label="Días disponibles para reservar"
            name="daysOfWeek"
            rules={[{ required: true, message: "Selecciona al menos un día" }]}
          >
            <Checkbox.Group options={daysOfWeekOptions} />
          </Form.Item>
        </>
      )}
    </Form>
  );
};
