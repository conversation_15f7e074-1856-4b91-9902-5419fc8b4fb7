import { Button, Form, Row, Select } from "antd";
import { months } from "../../utils.ts/time-utils";
import { MaintenanceFee } from "../../interfaces/maintenance-fee";
import { SearchOutlined } from "@ant-design/icons";

interface MonthyMaintenanceChargesSearchFormProps {
  existingMaintenanceFee: MaintenanceFee[];
  defaultMonth: number;
  defaultYear: number;
  onSearch: (month?: number, year?: number) => void;
}

export const MonthyMaintenanceChargesSearchForm: React.FC<
  MonthyMaintenanceChargesSearchFormProps
> = ({ existingMaintenanceFee, onSearch, defaultMonth, defaultYear }) => {
  const [form] = Form.useForm();

  return (
    <Form
      form={form}
      layout="inline"
      initialValues={{
        year: defaultYear,
        month: defaultMonth,
      }}
      onFinish={(values) => onSearch(values.month, values.year)}
    >
      <Row justify="end" style={{ width: "100%" }} gutter={16}>
        <Form.Item name="month">
          <Select
            placeholder="Mes"
            style={{ width: 120 }}
            options={months.map((month, index) => ({
              value: index + 1,
              label: month,
            }))}
          />
        </Form.Item>

        <Form.Item name="year">
          <Select
            placeholder="Año"
            style={{ width: 120 }}
            options={existingMaintenanceFee.map((maintenanceFee) => ({
              value: maintenanceFee.year,
              label: maintenanceFee.year,
            }))}
          />
        </Form.Item>

        <Form.Item>
          <Button
            htmlType="submit"
            iconPosition="end"
            icon={<SearchOutlined />}
            type="primary"
          >
            Buscar
          </Button>
        </Form.Item>
      </Row>
    </Form>
  );
};
