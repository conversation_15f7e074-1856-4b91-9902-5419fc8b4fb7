import { Checkbox, Form, Select } from "antd";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Property } from "../../interfaces/property";
import { User } from "../../interfaces/user";
import { ComplaintType } from "../../interfaces/complaint-type";
import {
  infracttionSeverityOptions,
  priorityOptions,
  rentalStatusOptions,
  reservationStatusOptions,
  statusOptions,
  taskStatusOptions,
} from "../../utils.ts/form-utils";
import { Supplier } from "../../interfaces/supplier";
import { Service } from "../../interfaces/service";
import { Role } from "../../interfaces/role";

export const PropertySelect: React.FC = () => {
  const { data: propertiesCached } = useCachedQuery<Property>(
    "property/for-select"
  );
  const properties = propertiesCached || [];

  return (
    <Form.Item
      name="propertyId"
      label="Propiedad"
      rules={[
        {
          required: true,
          message: "Por favor selecciona una propiedad",
        },
      ]}
    >
      <Select
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={properties.map((proeprty) => {
          return {
            value: proeprty.id,
            label: proeprty.address,
          };
        })}
      />
    </Form.Item>
  );
};

interface UserSelectProps {
  name?: string;
  label?: string;
}

export const UserSelect: React.FC<UserSelectProps> = ({ name, label }) => {
  const { data: usersCached } = useCachedQuery<User>("user/for-select");
  const users = usersCached || [];

  return (
    <Form.Item
      name={name ?? "userId"}
      label={label ?? "Usuario"}
      rules={[
        {
          required: true,
          message: "Por favor selecciona un usuario",
        },
      ]}
    >
      <Select
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={users.map((user) => {
          return {
            value: user.id,
            label: `${user.firstName} ${user.paternalLastName} ${user.maternalLastName}`,
          };
        })}
      />
    </Form.Item>
  );
};

export const ComplainTypeSelect: React.FC = () => {
  const { data: complaintTypesCached } = useCachedQuery<ComplaintType>(
    "complaint-type/for-select"
  );
  const complaintTypes = complaintTypesCached || [];

  return (
    <Form.Item
      name="complaintTypeId"
      label="Tipo de queja"
      rules={[
        {
          required: true,
          message: "Por favor selecciona un tipo de queja",
        },
      ]}
    >
      <Select
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={complaintTypes.map((complaintType) => {
          return {
            value: complaintType.id,
            label: complaintType.name,
          };
        })}
      />
    </Form.Item>
  );
};

export const FacilitySelect: React.FC = () => {
  const { data: facilitiesCached } = useCachedQuery<ComplaintType>(
    "facility/for-select"
  );
  const facilities = facilitiesCached || [];

  return (
    <Form.Item
      name="facilityId"
      label="Amenidad"
      rules={[
        {
          required: true,
          message: "Por favor selecciona una amenidad",
        },
      ]}
    >
      <Select
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={facilities.map((facility) => {
          return {
            value: facility.id,
            label: facility.name,
          };
        })}
      />
    </Form.Item>
  );
};

export const StatusSelect: React.FC<{ disabled: boolean }> = ({ disabled }) => {
  return (
    <Form.Item name="status" label="Status">
      <Select
        disabled={disabled}
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={statusOptions.map(({ value, label }) => {
          return {
            value,
            label,
          };
        })}
      />
    </Form.Item>
  );
};

export const PrioritySelect: React.FC<{ disabled: boolean }> = ({
  disabled,
}) => {
  return (
    <Form.Item name="priority" label="Prioridad">
      <Select
        disabled={disabled}
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={priorityOptions.map(({ value, label }) => {
          return {
            value,
            label,
          };
        })}
      />
    </Form.Item>
  );
};

export const InfractionSeveritySelect: React.FC<{ disabled: boolean }> = ({
  disabled,
}) => {
  return (
    <Form.Item name="severity" label="Severidad">
      <Select
        disabled={disabled}
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={infracttionSeverityOptions.map(({ value, label }) => {
          return {
            value,
            label,
          };
        })}
      />
    </Form.Item>
  );
};

export const ReservationStatusSelect: React.FC<{ disabled?: boolean }> = ({
  disabled,
}) => {
  return (
    <Form.Item name="status" label="Status">
      <Select
        disabled={disabled}
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={reservationStatusOptions.map(({ value, label }) => {
          return {
            value,
            label,
          };
        })}
      />
    </Form.Item>
  );
};

export const RentalStatusSelect: React.FC<{ disabled?: boolean }> = ({
  disabled,
}) => {
  return (
    <Form.Item name="status" label="Status">
      <Select
        disabled={disabled}
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={rentalStatusOptions.map(({ value, label }) => {
          return {
            value,
            label,
          };
        })}
      />
    </Form.Item>
  );
};

export const SupplierSelect: React.FC<{ required?: boolean }> = ({
  required = true,
}) => {
  const { data: suppliersCached } = useCachedQuery<Supplier>(
    "supplier/for-select"
  );
  const suppliers = suppliersCached || [];

  return (
    <Form.Item
      name="supplierId"
      label="Proveedor"
      rules={
        required
          ? [
              {
                required: true,
                message: "Por favor selecciona un proveedor",
              },
            ]
          : []
      }
    >
      <Select
        optionFilterProp="label"
        showSearch
        allowClear={!required}
        placeholder={
          required
            ? "Seleccionar proveedor"
            : "Seleccionar proveedor (opcional)"
        }
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: "100%" }}
        options={suppliers.map((supplier) => {
          return {
            value: supplier.id,
            label: supplier.name,
          };
        })}
      />
    </Form.Item>
  );
};

export const RolesSelect: React.FC = () => {
  const { data: rolesCached } = useCachedQuery<Role>("role/for-select");
  const roles = rolesCached || [];
  return (
    <Form.Item
      name="roles"
      label="Roles"
      rules={[
        {
          required: true,
          message: "Por favor selecciona un rol",
        },
      ]}
    >
      <Checkbox.Group
        options={roles.map((role) => {
          return {
            value: role.id,
            label: role.name,
          };
        })}
      />
    </Form.Item>
  );
};

export const RoleSelect: React.FC = () => {
  const { data: rolesCached } = useCachedQuery<Role>("role/for-select");
  const roles = rolesCached || [];
  return (
    <Form.Item
      name="roleId"
      label="Rol"
      rules={[
        {
          required: true,
          message: "Por favor selecciona un rol",
        },
      ]}
    >
      <Select
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={roles.map((rol) => {
          return {
            value: rol.id,
            label: rol.name,
          };
        })}
      />
    </Form.Item>
  );
};

export const TaskStatusSelect: React.FC<{ disabled?: boolean }> = ({
  disabled,
}) => {
  return (
    <Form.Item name="status" label="Estado">
      <Select
        disabled={disabled}
        optionFilterProp="label"
        showSearch
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: 120 }}
        options={taskStatusOptions.map(({ value, label }) => {
          return {
            value,
            label,
          };
        })}
      />
    </Form.Item>
  );
};

export const ServiceSelect: React.FC<{ required?: boolean }> = ({
  required = false,
}) => {
  const { data: servicesCached } =
    useCachedQuery<Service>("service/for-select");
  const services = servicesCached || [];

  return (
    <Form.Item
      name="serviceId"
      label="Servicio"
      rules={
        required
          ? [
              {
                required: true,
                message: "Por favor selecciona un servicio",
              },
            ]
          : []
      }
    >
      <Select
        optionFilterProp="label"
        showSearch
        allowClear
        placeholder={
          required ? "Seleccionar servicio" : "Seleccionar servicio (opcional)"
        }
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
        style={{ width: "100%" }}
        options={services.map((service) => {
          return {
            value: service.id,
            label: service.name,
          };
        })}
      />
    </Form.Item>
  );
};

//Number select receive max numbers of select and returns number

interface NumberSelectProps {
  name: string;
  label: string;
  max: number;
  required?: boolean;
}

export const NumberSelect: React.FC<NumberSelectProps> = ({
  name,
  label,
  max,
  required = false,
}) => {
  const numbers = Array.from({ length: max }, (_, i) => i + 1);

  return (
    <Form.Item
      name={name}
      label={label}
      initialValue={1}
      rules={
        required
          ? [
              {
                required: true,
                message: `Por favor selecciona un ${label}`,
              },
            ]
          : []
      }
    >
      <Select<number>
        placeholder={`Seleccionar ${label}${required ? "" : " (opcional)"}`}
        allowClear={!required}
        showSearch
        style={{ width: "100%" }}
        filterOption={(input, option) =>
          (option?.label ?? "")
            .toString()
            .toLowerCase()
            .includes(input.toLowerCase())
        }
        options={numbers.map((number) => ({
          value: number,
          label: number.toString(),
        }))}
      />
    </Form.Item>
  );
};
