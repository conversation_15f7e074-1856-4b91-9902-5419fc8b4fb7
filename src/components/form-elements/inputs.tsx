import { Form, Input } from "antd";
import { MailOutlined, UserOutlined } from "@ant-design/icons";

export const HiddenId: React.FC = () => {
  return (
    <Form.Item name="id" label="id" hidden>
      <Input />
    </Form.Item>
  );
};

export const NameInput: React.FC = () => {
  return (
    <Form.Item
      name="name"
      label="Nombre"
      rules={[
        {
          required: true,
          message: "¡Por favor ingresa un nombre!",
        },
      ]}
    >
      <Input />
    </Form.Item>
  );
};

export const DescriptionInput: React.FC = () => {
  return (
    <Form.Item
      name="description"
      label="Descripción"
      rules={[
        {
          required: true,
          message: "¡Por favor ingresa una descripción!",
        },
      ]}
    >
      <Input.TextArea rows={6} maxLength={300} />
    </Form.Item>
  );
};

export const BasicUserInfoInputs: React.FC = () => {
  return (
    <>
      <Form.Item
        name="email"
        label="Correo Electrónico"
        rules={[
          {
            required: true,
            message: "Por favor ingresa tu correo electrónico",
          },
          {
            type: "email",
            message: "Por favor ingresa un correo electrónico válido",
          },
        ]}
      >
        <Input prefix={<MailOutlined />} placeholder="<EMAIL>" />
      </Form.Item>

      <Form.Item
        name="firstName"
        label="Nombre"
        rules={[{ required: true, message: "Por favor ingresa tu nombre" }]}
      >
        <Input prefix={<UserOutlined />} placeholder="Juan" />
      </Form.Item>

      <Form.Item
        name="paternalLastName"
        label="Apellido Paterno"
        rules={[
          {
            required: true,
            message: "Por favor ingresa tu apellido paterno",
          },
        ]}
      >
        <Input prefix={<UserOutlined />} placeholder="Pérez" />
      </Form.Item>

      <Form.Item
        name="maternalLastName"
        label="Apellido Materno"
        rules={[
          {
            required: true,
            message: "Por favor ingresa tu apellido materno",
          },
        ]}
      >
        <Input prefix={<UserOutlined />} placeholder="García" />
      </Form.Item>

      <Form.Item
        name="phone"
        label="Teléfono"
        rules={[
          {
            required: true,
            message: "Por favor ingresa tu número telefónico",
          },
        ]}
      >
        <Input prefix={<MailOutlined />} placeholder="4425111115" />
      </Form.Item>
    </>
  );
};

export const TitleInput: React.FC = () => {
  return (
    <Form.Item
      name="title"
      label="Título"
      rules={[
        {
          required: true,
          message: "Por favor ingresa un título",
        },
      ]}
    >
      <Input />
    </Form.Item>
  );
};
