import { useState } from "react";
import { Image, Upload, message } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import type { UploadFile } from "antd/es/upload/interface";
import type { RcFile } from "antd/es/upload";

interface ImageUploadProps {
  maxFiles?: number;
  value?: UploadFile[];
  onChange?: (files: UploadFile[]) => void;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  maxFiles = 3,
  value = [],
  onChange,
}) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const handleBeforeUpload = async (file: RcFile) => {
    const isImage = file.type.startsWith("image/");
    const isTooLarge = file.size / 1024 / 1024 > 2;

    if (!isImage) {
      message.error("Solo se permiten imágenes.");
      return Upload.LIST_IGNORE;
    }

    if (isTooLarge) {
      message.error("La imagen debe ser menor a 2MB.");
      return Upload.LIST_IGNORE;
    }

    // Generar preview
    const preview = await new Promise<string>((resolve) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
    });

    const uploadFile: UploadFile = {
      uid: file.uid,
      name: file.name,
      status: "done", // importante para que se muestre como cargado
      url: preview,
      originFileObj: file,
    };

    onChange?.([...value, uploadFile]);
    return Upload.LIST_IGNORE; // evita carga automática
  };

  const handleRemove = (file: UploadFile) => {
    onChange?.(value.filter((f) => f.uid !== file.uid));
  };

  const handlePreview = async (file: UploadFile) => {
    let src = file.url ?? file.preview;

    if (!src && file.originFileObj) {
      src = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj as RcFile);
        reader.onload = () => resolve(reader.result as string);
      });
    }

    if (src) {
      setPreviewImage(src);
      setPreviewOpen(true);
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Subir</div>
    </div>
  );

  return (
    <>
      <Upload
        accept="image/*"
        listType="picture-card"
        fileList={value}
        onPreview={handlePreview}
        onRemove={handleRemove}
        beforeUpload={handleBeforeUpload}
        maxCount={maxFiles}
      >
        {value.length >= maxFiles ? null : uploadButton}
      </Upload>
      {previewImage && (
        <Image
          wrapperStyle={{ display: "none" }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(""),
          }}
          src={previewImage}
        />
      )}
    </>
  );
};
