import { Tag } from "antd";
import { Infraction } from "../../interfaces/infraction";

interface InfractionsProps {
  infractions: Infraction[];
}

export const Infractions: React.FC<InfractionsProps> = ({ infractions }) => {
  if (infractions.length) {
    return infractions.map((infraction) => (
      <Tag key={`${infraction.id}-key`} color="gray">
        {infraction.date} {infraction.description}
      </Tag>
    ));
  }
  return <Tag color="green">Sin infracciones</Tag>;
};
