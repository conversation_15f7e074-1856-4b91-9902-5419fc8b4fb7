import { Tag } from "antd";
import { Visit } from "../../interfaces/visit";

interface VisitsProps {
  visits: Visit[];
}

export const Visits: React.FC<VisitsProps> = ({ visits }) => {
  if (visits.length) {
    return visits.map((visit) => (
      <Tag key={`${visit.id}-key`} color="gray">
        {visit.vehiclePlate} {visit.visitorName}
      </Tag>
    ));
  }
  return <Tag color="gray">Sin visitas</Tag>;
};
