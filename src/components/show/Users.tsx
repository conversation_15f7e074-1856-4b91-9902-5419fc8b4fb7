import { Button, Tag } from "antd";
import { User } from "../../interfaces/user";
import { ResidentIcon } from "../icons";

interface UsersProps {
  users: User[];
}

export const Users: React.FC<UsersProps> = ({ users }) => {
  if (users.length) {
    return users.map((resident) => {
      return (
        <Button
          key={`${resident.id}-key`}
          onClick={() => console.log(resident)}
          icon={<ResidentIcon resident={resident} />}
          type="link"
        >
          {resident.firstName} {resident.paternalLastName}
        </Button>
      );
    });
  }
  return <Tag color="gray">Sin residentes</Tag>;
};
