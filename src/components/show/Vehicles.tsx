import { Tag } from "antd";
import { Vehicle } from "../../interfaces/vehicle";

interface VehiclesProps {
  vehicles: Vehicle[];
}

export const Vehicles: React.FC<VehiclesProps> = ({ vehicles }) => {
  if (vehicles.length) {
    return vehicles.map((vehicle) => (
      <Tag key={`${vehicle.id}-key`} color="gray">
        {vehicle.plate} {vehicle.brand}
      </Tag>
    ));
  }
  return <Tag color="gray">Sin vehiculos</Tag>;
};
