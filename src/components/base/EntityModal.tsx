import { Modal, Typography } from "antd";
import React from "react";

const { Title } = Typography;

interface EntityModalProps {
  isModalOpen: boolean;
  isEditMode: boolean;
  handleOk: () => void;
  handleCancel: () => void;
  title: string;
  children: React.ReactNode;
}

export const EntityModal: React.FC<EntityModalProps> = ({
  isModalOpen,
  isEditMode,
  handleOk,
  handleCancel,
  title,
  children,
}) => {
  return (
    <Modal
      title={
        <Title level={4}>
          {isEditMode ? `Editar ${title}` : `Crear ${title}`}
        </Title>
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      onClose={handleCancel}
    >
      {children}
    </Modal>
  );
};
