import { Table, TableProps } from "antd";
import { Actions } from "../tables/table-elements/Actions";

interface EntityTableProps<T extends { id: string }> {
  data: T[];
  baseColumns: TableProps<any>["columns"];
  mapToRow: (item: T) => any;
  onEdit?: (item: T) => void;
  onDelete?: (id: string) => void;
}

export const EntityTable = <T extends { id: string }>({
  data,
  baseColumns,
  mapToRow,
  onEdit,
  onDelete,
}: EntityTableProps<T>) => {
  const rows = data.map(mapToRow);

  const columns: TableProps<any>["columns"] = [
    ...(baseColumns ?? []),
    {
      title: "Acciones",
      key: "acciones",
      render: (item: T) => (
        <Actions
          entity={item}
          id={item.id}
          onEdit={onEdit}
          onDelete={onDelete}
          url={item.id}
        />
      ),
    },
  ];

  return <Table columns={columns} dataSource={rows} />;
};
