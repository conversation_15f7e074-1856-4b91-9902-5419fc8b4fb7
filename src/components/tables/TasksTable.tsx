import { Table } from "antd";
import type { TableProps } from "antd";
import { Task } from "../../interfaces/task";

interface DataType {
  key: string;
  title: Task["title"];
  description: Task["description"];
  status: Task["status"];
  dueDate: Task["dueDate"];
  completedAt: Task["completedAt"];
}

interface TasksTableProps {
  tasks: Task[];
}

export const TasksTable: React.FC<TasksTableProps> = ({ tasks }) => {
  const data: DataType[] = tasks.map((task) => {
    return {
      key: task.id,
      title: task.title,
      description: task.description,
      status: task.status,
      dueDate: task.dueDate,
      completedAt: task.completedAt,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Título",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "Descripción",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
    },
    {
      title: "Fecha límite",
      dataIndex: "dueDate",
      key: "dueDate",
    },
    {
      title: "Completado en",
      dataIndex: "completedAt",
      key: "completedAt",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
