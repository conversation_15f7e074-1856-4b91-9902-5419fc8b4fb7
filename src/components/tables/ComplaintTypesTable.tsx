import { Button, Table } from "antd";
import type { TableProps } from "antd";
import { ComplaintType } from "../../interfaces/complaint-type";

interface DataType {
  key: ComplaintType["id"];
  name: ComplaintType;
  description: ComplaintType["description"];
}

interface ComplaintTypesTableProps {
  complaintTypes: ComplaintType[];
  onEdit?: (complaintType: ComplaintType) => void;
}

export const ComplaintTypesTable: React.FC<ComplaintTypesTableProps> = ({
  complaintTypes,
  onEdit,
}) => {
  const data: DataType[] = complaintTypes.map((complaintType) => {
    return {
      key: complaintType.id,
      name: complaintType,
      description: complaintType.description,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Nombre",
      dataIndex: "name",
      key: "name",
      render: (complaintType: ComplaintType) => {
        return (
          <Button type="link" onClick={() => onEdit?.(complaintType)}>
            {complaintType.name}
          </Button>
        );
      },
    },
    {
      title: "Descripción",
      dataIndex: "description",
      key: "description",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
