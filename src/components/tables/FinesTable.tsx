import { Table } from "antd";
import type { TableProps } from "antd";
import { Fine } from "../../interfaces/fine";

interface DataType {
  key: Fine["id"];
  amount: Fine["amount"];
  description: Fine["description"];
  issuedAt: Fine["issuedAt"];
  isPaid: Fine["isPaid"];
}

interface FinesTableProps {
  fines: Fine[];
}

export const FinesTable: React.FC<FinesTableProps> = ({ fines }) => {
  const data: DataType[] = fines.map((fine) => {
    return {
      key: fine.id,
      amount: fine.amount,
      description: fine.description,
      issuedAt: fine.issuedAt,
      isPaid: fine.isPaid,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Monto",
      dataIndex: "amount",
      key: "amount",
    },
    {
      title: "Descripción",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Emitido el",
      dataIndex: "issuedAt",
      key: "issuedAt",
    },
    {
      title: "Pa<PERSON>",
      dataIndex: "isPaid",
      key: "isPaid",
      render: (isPaid: boolean) => {
        return <p>{isPaid ? "Pagado" : "No pagado"}</p>;
      },
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
