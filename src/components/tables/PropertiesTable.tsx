import { Input, Table } from "antd";
import type { TableProps } from "antd";
import { Property } from "../../interfaces/property";
import { ActionEntityButton } from "../buttons/ActionEntityButton";
import { Users } from "../show";
import { useState } from "react";
import { Actions } from "./table-elements/Actions";

interface DataType {
  key: string;
  id: Property["id"];
  address: Property["address"];
  residents: Property["residents"];
  vehicles: Property["vehicles"];
  visits: Property["visits"];
  infractions: Property["infractions"];
}

interface PropertiesTableProps {
  properties: Property[];
}

export const PropertiesTable: React.FC<PropertiesTableProps> = ({
  properties,
}) => {
  const [searchText, setSearchText] = useState("");

  // 🔍 Filtra los datos según el término de búsqueda
  const filteredData = properties.filter((property) =>
    property.address.toLowerCase().includes(searchText.toLowerCase())
  );

  const data: DataType[] = filteredData.map((property) => {
    return {
      key: property.id,
      id: property.id,
      address: property.address,
      residents: property.residents,
      vehicles: property.vehicles,
      infractions: property.infractions,
      visits: property.visits,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Propiedad",
      dataIndex: "address",
      key: "address",
    },
    {
      title: "Residentes",
      dataIndex: "residents",
      key: "residents",
      render: (residents: Property["residents"]) => {
        return <Users users={residents} />;
      },
    },
    {
      title: "Vehiculos",
      dataIndex: "vehicles",
      key: "vehicles",
      render: (vehicles: Property["vehicles"]) => {
        return (
          <ActionEntityButton
            data={vehicles}
            getUrl={(vehicle) => `/dashboard/vehicles/${vehicle.id}`}
            getLabel={(vehicle) => vehicle.plate}
            emptyText="Sin vehiculos"
          />
        );
      },
    },
    {
      title: "Infracciones",
      dataIndex: "infractions",
      key: "infractions",
      render: (infractions: Property["infractions"]) => {
        return <p>{infractions.length}</p>;
      },
    },
    {
      title: "Visitas",
      dataIndex: "visits",
      key: "visits",
      render: (visits: Property["visits"]) => {
        const avtiveVisits = visits.filter(
          (visit) => visit.checkOutTime === null
        );
        return (
          <ActionEntityButton
            data={avtiveVisits}
            getUrl={(visit) => `/dashboard/visits/${visit.id}`}
            getLabel={(visit) => visit.vehiclePlate ?? ""}
            emptyText="Sin visitas activas"
          />
        );
      },
    },
    {
      title: "Acciones",
      dataIndex: "id",
      key: "id",
      render: (id: Property["id"]) => {
        return <Actions url={id} />;
      },
    },
  ];
  return (
    <>
      {/* 🔍 Input de búsqueda */}
      <Input.Search
        placeholder="Buscar por dirección"
        allowClear
        onChange={(e) => setSearchText(e.target.value)}
        style={{ marginBottom: 16, width: 300 }}
      />

      <Table<DataType> columns={columns} dataSource={data} />
    </>
  );
};
