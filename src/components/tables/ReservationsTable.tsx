import { Table } from "antd";
import type { TableProps } from "antd";
import { Reservation } from "../../interfaces/reservation";
import { formatLongDateTime } from "../../utils.ts/time-utils";

interface DataType {
  key: Reservation["id"];
  amountOfPeople: Reservation["amountOfPeople"];
  date: Reservation["date"];
  status: Reservation["status"];
}

interface ReservationsTableProps {
  reservations: Reservation[];
}

export const ReservationsTable: React.FC<ReservationsTableProps> = ({
  reservations,
}) => {
  const data: DataType[] = reservations.map((reservation) => {
    return {
      key: reservation.id,
      date: reservation.date,
      status: reservation.status,
      amountOfPeople: reservation.amountOfPeople,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "# Asistentes",
      dataIndex: "amountOfPeople",
      key: "amountOfPeople",
    },
    {
      title: "Fecha",
      dataIndex: "date",
      key: "date",
      render: (date: Reservation["date"]) => {
        return <p>{formatLongDateTime(date)}</p>;
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
