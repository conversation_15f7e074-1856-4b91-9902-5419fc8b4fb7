import React from "react";
import { Flex, Table } from "antd";
import type { TableProps } from "antd";
import { Property } from "../../interfaces/property";
import { User } from "../../interfaces/user";
import { Role } from "../../interfaces/role";
import { ActionEntityButton } from "../buttons/ActionEntityButton";
import { Actions } from "./table-elements/Actions";

interface DataType {
  key: string;
  name: string;
  email: User["email"];
  phone: User["phone"];
  properties: Property[];
  roles: Role[];
  user: User;
}

interface UsersTableProps {
  users: User[];
  onEdit?: (user: User) => void;
  onDelete?: (id: User["id"]) => void;
}

export const UsersTable: React.FC<UsersTableProps> = ({
  users,
  onEdit,
  onDelete,
}) => {
  const data: DataType[] = users.map((user) => {
    return {
      key: user.id,
      name: `${user.firstName} ${user.paternalLastName} ${user.maternalLastName}`,
      email: user.email,
      phone: user.phone,
      properties: user.properties,
      roles: user.roles,
      user: user,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Usuario",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Correo",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Telefono",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "Propiedades",
      dataIndex: "properties",
      key: "properties",
      render: (properties: DataType["properties"]) => {
        return (
          <ActionEntityButton
            data={properties}
            getUrl={(property) => `/dashboard/properties/${property.id}`}
            getLabel={(property) => property.address}
            emptyText="Sin propiedades"
          />
        );
      },
    },
    {
      title: "Roles",
      dataIndex: "roles",
      key: "roles",
      render: (roles: DataType["roles"]) => {
        return roles.map((role) => <p key={role.id}>{role.name}</p>);
      },
    },
    {
      title: "Acciones",
      dataIndex: "user",
      key: "user",
      render: (user: User) => {
        return (
          <Actions
            entity={user}
            id={user.id}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        );
      },
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
