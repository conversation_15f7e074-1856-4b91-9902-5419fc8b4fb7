import { Table } from "antd";
import type { TableProps } from "antd";
import { Supplier } from "../../interfaces/supplier";

interface DataType {
  key: Supplier["id"];
  name: Supplier["name"];
  phone: Supplier["phone"];
  email: Supplier["email"];
  address: Supplier["address"];
}

interface SuppliersTableProps {
  suppliers: Supplier[];
}

export const SuppliersTable: React.FC<SuppliersTableProps> = ({
  suppliers,
}) => {
  const data: DataType[] = suppliers.map((supplier) => {
    return {
      key: supplier.id,
      name: supplier.name,
      phone: supplier.phone,
      email: supplier.email,
      address: supplier.address,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Nombre",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Teléfono",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "Correo",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Dirección",
      dataIndex: "address",
      key: "address",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
