import { Table } from "antd";
import type { TableProps } from "antd";
import { Announcement } from "../../interfaces/announcement";

interface DataType {
  key: Announcement["id"];
  title: Announcement["title"];
  message: Announcement["message"];
  createdAt: Announcement["createdAt"];
}

interface AnnouncementsTableProps {
  announcements: Announcement[];
}

export const AnnouncementsTable: React.FC<AnnouncementsTableProps> = ({
  announcements,
}) => {
  const data: DataType[] = announcements.map((announcement) => {
    return {
      key: announcement.id,
      title: announcement.title,
      message: announcement.message,
      createdAt: announcement.createdAt,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Título",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "Mensaje",
      dataIndex: "message",
      key: "message",
    },
    {
      title: "Creado el",
      dataIndex: "createdAt",
      key: "createdAt",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
