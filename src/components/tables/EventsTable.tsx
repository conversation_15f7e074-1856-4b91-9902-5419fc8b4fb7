import { Table } from "antd";
import type { TableProps } from "antd";
import { Event } from "../../interfaces/event";

interface DataType {
  key: Event["id"];
  title: Event["title"];
  description: Event["description"];
  location: Event["location"];
  startDate: Event["startDate"];
  endDate: Event["endDate"];
}

interface EventsTableProps {
  events: Event[];
}

export const EventsTable: React.FC<EventsTableProps> = ({ events }) => {
  const data: DataType[] = events.map((event) => {
    return {
      key: event.id,
      title: event.title,
      description: event.description,
      location: event.location,
      startDate: event.startDate,
      endDate: event.endDate,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Nombre",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Título",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "Descripción",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Ubicación",
      dataIndex: "location",
      key: "location",
    },
    {
      title: "Inicia",
      dataIndex: "startDate",
      key: "startDate",
    },
    {
      title: "Termina",
      dataIndex: "endDate",
      key: "endDate",
    },
    {
      title: "Público",
      dataIndex: "isPublic",
      key: "isPublic",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
