import { Table } from "antd";
import type { TableProps } from "antd";
import { Employee } from "../../interfaces/employee";

interface DataType {
  key: Employee["id"];
  name: string;
  position: Employee["position"];
  phone: Employee["phone"];
  email: Employee["email"];
  hireDate: Employee["hireDate"];
}

interface EmployeesTableProps {
  employees: Employee[];
}

export const EmployeesTable: React.FC<EmployeesTableProps> = ({
  employees,
}) => {
  const data: DataType[] = employees.map((employee) => {
    return {
      key: employee.id,
      name: `${employee.firstName} ${employee.maternalLastName} ${employee.paternalLastName}`,
      position: employee.position,
      phone: employee.phone,
      email: employee.email,
      hireDate: employee.hireDate,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Nombre",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Cargo",
      dataIndex: "position",
      key: "position",
    },
    {
      title: "Teléfono",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "Correo",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Fecha de contratación",
      dataIndex: "hireDate",
      key: "hireDate",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
