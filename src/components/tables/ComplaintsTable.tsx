import { Button, Table } from "antd";
import type { TableProps } from "antd";
import { Complaint } from "../../interfaces/complaint";
import { getPriorityLabel, getStatusLabel } from "../../utils.ts/form-utils";
import { formatShortDate } from "../../utils.ts/time-utils";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Property } from "../../interfaces/property";
import { User } from "../../interfaces/user";
import { ComplaintType } from "../../interfaces/complaint-type";
import { EditOutlined } from "@ant-design/icons";
import { Actions } from "./table-elements/Actions";

interface DataType {
  key: Complaint["id"];
  complaint: Complaint;
  propertyId: Complaint["propertyId"];
  userId: Complaint["userId"];
  complaintTypeId: Complaint["complaintTypeId"];
  detail: Complaint["detail"];
  status: Complaint["status"];
  priority: Complaint["priority"];
  createdAt: Complaint["createdAt"];
  completedAt: Complaint["completedAt"];
}

interface ComplaintsTableProps {
  complaints: Complaint[];
  onEdit?: (complaint: Complaint) => void;
}

export const ComplaintsTable: React.FC<ComplaintsTableProps> = ({
  complaints,
  onEdit,
}) => {
  const { data: propertiesCached } = useCachedQuery<Property>(
    "property/for-select"
  );
  const properties = propertiesCached || [];

  const getProperty = (propertyId: Property["id"]) => {
    return properties.find((property) => property.id === propertyId);
  };

  const { data: usersCached } = useCachedQuery<User>("user/for-select");
  const users = usersCached || [];

  const getUser = (userId: User["id"]) => {
    return users.find((user) => user.id === userId);
  };

  const { data: complaintTypesCached } = useCachedQuery<ComplaintType>(
    "complaint-type/for-select"
  );
  const complaintTypes = complaintTypesCached || [];

  const getComplaintType = (complaintTypeId: ComplaintType["id"]) => {
    return complaintTypes.find(
      (complaintType) => complaintType.id === complaintTypeId
    );
  };

  const data: DataType[] = complaints.map((complaint) => {
    return {
      key: complaint.id,
      complaint: complaint,
      propertyId: complaint.propertyId,
      userId: complaint.userId,
      complaintTypeId: complaint.complaintTypeId,
      detail: complaint.detail,
      status: complaint.status,
      priority: complaint.priority,
      createdAt: complaint.createdAt,
      completedAt: complaint.completedAt,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Propiedad",
      dataIndex: "propertyId",
      key: "propertyId",
      render: (propertyId: Complaint["propertyId"]) => {
        return <p>{getProperty(propertyId)?.address ?? ""}</p>;
      },
    },
    {
      title: "Usuario",
      dataIndex: "userId",
      key: "userId",
      render: (userId: Complaint["userId"]) => {
        const user = getUser(userId);
        return (
          <p>{`${user?.firstName} ${user?.paternalLastName} ${user?.paternalLastName}`}</p>
        );
      },
    },
    {
      title: "Tipo de queja",
      dataIndex: "complaintTypeId",
      key: "complaintTypeId",
      render: (complaintTypeId: Complaint["complaintTypeId"]) => {
        const complaintType = getComplaintType(complaintTypeId);
        return <p>{complaintType?.name}</p>;
      },
    },
    {
      title: "Detalle",
      dataIndex: "detail",
      key: "detail",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: Complaint["status"]) => {
        return <p>{getStatusLabel(status)}</p>;
      },
    },
    {
      title: "Prioridad",
      dataIndex: "priority",
      key: "priority",
      render: (priority: Complaint["priority"]) => {
        return <p>{getPriorityLabel(priority)}</p>;
      },
    },
    {
      title: "Creado el",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (createdAt: Complaint["createdAt"]) => {
        return <p>{formatShortDate(createdAt)}</p>;
      },
    },
    {
      title: "Completado el",
      dataIndex: "completedAt",
      key: "completedAt",
      render: (completedAt: Complaint["completedAt"]) => {
        return <p>{formatShortDate(completedAt)}</p>;
      },
    },
    {
      title: "Acciones",
      dataIndex: "complaint",
      key: "complaint",
      render: (complaint: Complaint) => {
        return <Actions entity={complaint} onEdit={onEdit} />;
      },
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
