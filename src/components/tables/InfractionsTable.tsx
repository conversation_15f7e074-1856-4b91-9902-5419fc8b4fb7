import { <PERSON><PERSON>, <PERSON>lex, <PERSON>confirm, Table } from "antd";
import type { TableProps } from "antd";
import { Infraction } from "../../interfaces/infraction";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import { Actions } from "./table-elements/Actions";

interface DataType {
  key: Infraction["id"];
  description: Infraction["description"];
  date: Infraction["date"];
  severity: Infraction["severity"];
  infraction: Infraction;
}

interface InfractionsTableProps {
  infractions: Infraction[];
  onEdit?: (infraction: Infraction) => void;
  onDelete?: (id: string) => void;
}

export const InfractionsTable: React.FC<InfractionsTableProps> = ({
  infractions,
  onEdit,
  onDelete,
}) => {
  const data: DataType[] = infractions.map((infraction) => {
    return {
      key: infraction.id,
      description: infraction.description,
      date: infraction.date,
      severity: infraction.severity,
      infraction: infraction,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Descripción",
      dataIndex: "description",
      key: "description",
      render: (text: string) => (
        <div
          style={{
            maxWidth: 200,
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: "Fecha",
      dataIndex: "date",
      key: "date",
    },
    {
      title: "Severidad",
      dataIndex: "severity",
      key: "severity",
    },
    {
      title: "Acciones",
      key: "infraction",
      dataIndex: "infraction",
      render: (infraction: Infraction) => (
        <Actions
          entity={infraction}
          id={infraction.id}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      ),
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
