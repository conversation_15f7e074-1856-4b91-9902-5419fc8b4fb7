import { Table } from "antd";
import type { TableProps } from "antd";
import { Expense } from "../../interfaces/expense";

interface DataType {
  key: Expense["id"];
  amount: Expense["amount"];
  date: Expense["date"];
  receipt: Expense["receipt"];
}

interface ExpensesTableProps {
  expenses: Expense[];
}

export const ExpensesTable: React.FC<ExpensesTableProps> = ({ expenses }) => {
  const data: DataType[] = expenses.map((expense) => {
    return {
      key: expense.id,
      amount: expense.amount,
      date: expense.date,
      receipt: expense.receipt,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Monto",
      dataIndex: "amount",
      key: "amount",
    },
    {
      title: "Fecha de pago",
      dataIndex: "ExpenseDate",
      key: "ExpenseDate",
    },
    {
      title: "Recibo / Factura",
      dataIndex: "string",
      key: "string",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
