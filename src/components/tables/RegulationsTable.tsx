import { Button, Flex, Table } from "antd";
import type { TableProps } from "antd";
import { Regulation } from "../../interfaces/regulation";
import { CloudDownloadOutlined } from "@ant-design/icons";
import { Actions } from "./table-elements/Actions";

interface DataType {
  key: Regulation["id"];
  title: Regulation["title"];
  content: Regulation["content"];
  type: Regulation["type"];
  regulation: Regulation;
}

interface RegulationsTableProps {
  regulations: Regulation[];
  onEdit?: (regulation: Regulation) => void;
  onDelete?: (id: Regulation["id"]) => void;
}

export const RegulationsTable: React.FC<RegulationsTableProps> = ({
  regulations,
  onEdit,
  onDelete,
}) => {
  const data: DataType[] = regulations.map((regulation) => {
    return {
      key: regulation.id,
      title: regulation.title,
      content: regulation.content,
      type: regulation.type,
      regulation: regulation,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Título",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "Tipo",
      dataIndex: "type",
      key: "type",
    },
    {
      title: "Contenido",
      dataIndex: "content",
      key: "content",
    },
    {
      title: "Archivo",
      dataIndex: "fileUrl",
      key: "fileUrl",
      render: (fileUrl) =>
        fileUrl ? (
          <Button
            href={fileUrl}
            target="_blank"
            rel="noopener noreferrer"
            icon={<CloudDownloadOutlined />}
          />
        ) : (
          "No disponible"
        ),
    },
    {
      title: "Acciones",
      key: "regulation",
      dataIndex: "regulation",
      render: (regulation: Regulation) => (
        <Flex gap={10}>
          {regulation.fileUrl && (
            <Button
              href={regulation.fileUrl}
              target="_blank"
              rel="noopener noreferrer"
              icon={<CloudDownloadOutlined />}
            />
          )}
          <Actions
            entity={regulation}
            id={regulation.id}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        </Flex>
      ),
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
