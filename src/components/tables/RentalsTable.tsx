import { Table } from "antd";
import type { TableProps } from "antd";
import { Rental } from "../../interfaces/rental";

interface DataType {
  key: Rental["id"];
  startDate: Rental["startDate"];
  endDate: Rental["endDate"];
  monthlyRate: Rental["monthlyRate"];
  status: Rental["status"];
}

interface RentalsTableProps {
  rentals: Rental[];
}

export const RentalsTable: React.FC<RentalsTableProps> = ({ rentals }) => {
  const data: DataType[] = rentals.map((rental) => {
    return {
      key: rental.id,
      startDate: rental.startDate,
      endDate: rental.endDate,
      monthlyRate: rental.monthlyRate,
      status: rental.status,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Inicio",
      dataIndex: "startDate",
      key: "startDate",
    },
    {
      title: "Final",
      dataIndex: "endDate",
      key: "endDate",
    },
    {
      title: "Monto",
      dataIndex: "monthlyRate",
      key: "monthlyRate",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
