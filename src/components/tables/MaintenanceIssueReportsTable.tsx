import { Table } from "antd";
import type { TableProps } from "antd";
import { MaintenanceIssueReport } from "../../interfaces/maintenance-issue-report";

interface DataType {
  key: MaintenanceIssueReport["id"];
  description: MaintenanceIssueReport["description"];
  status: MaintenanceIssueReport["status"];
  createdAt: MaintenanceIssueReport["createdAt"];
}

interface MaintenanceIssueReportsTableProps {
  maintenanceIssueReports: MaintenanceIssueReport[];
}

export const MaintenanceIssueReportsTable: React.FC<
MaintenanceIssueReportsTableProps
> = ({ maintenanceIssueReports }) => {
  const data: DataType[] = maintenanceIssueReports.map((maintenanceIssueReport) => {
    return {
      key: maintenanceIssueReport.id,
      description: maintenanceIssueReport.description,
      status: maintenanceIssueReport.status,
      createdAt: maintenanceIssueReport.createdAt,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Descripción",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
    },
    {
      title: "Creado el",
      dataIndex: "createdAt",
      key: "createdAt",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
