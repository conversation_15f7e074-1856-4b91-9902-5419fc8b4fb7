import { Facility } from "../../interfaces/facility";
import { EntityTable } from "../base/EntityTable";

export const FacilitiesTable = ({
  facilities,
  onEdit,
  onDelete,
}: {
  facilities: Facility[];
  onEdit?: (facility: Facility) => void;
  onDelete?: (id: string) => void;
}) => {
  return (
    <EntityTable
      data={facilities}
      mapToRow={(facility) => ({
        ...facility,
        key: facility.id,
      })}
      baseColumns={[
        { title: "Nombre", dataIndex: "name", key: "name" },
        { title: "Descripción", dataIndex: "description", key: "description" },
      ]}
      onEdit={onEdit}
      onDelete={onDelete}
    />
  );
};
