import React from "react";
import { Table, Tag } from "antd";
import type { TableProps } from "antd";
import { ParkingSpot } from "../../interfaces/parking-spot";

interface DataType {
  key: string;
  spotNumber: string;
  isAvailable: boolean;
  type: string;
}

const columns: TableProps<DataType>["columns"] = [
  {
    title: "Número",
    dataIndex: "spotNumber",
    key: "spotNumber",
  },
  {
    title: "Disponible",
    dataIndex: "isAvailable",
    key: "isAvailable",
    render: (isAvailable) => (
      <Tag color={isAvailable ? "green" : "grey"}>
        {isAvailable ? "Disponible" : "No disponible"}
      </Tag>
    ),
  },
  {
    title: "Tipo",
    dataIndex: "type",
    key: "type",
    render: (type) => (
      <Tag color={type === "VISITOR" ? "yellow" : "blue"}>
        {type === "VISITOR" ? "Visitas" : "Residentes"}
      </Tag>
    ),
  },
];

interface ParkingSpotsTableProps {
  parkingSpots: ParkingSpot[];
}

export const ParkingSpotsTable: React.FC<ParkingSpotsTableProps> = ({
  parkingSpots,
}) => {
  const data: DataType[] = parkingSpots.map((parkingSpot) => {
    return {
      key: parkingSpot.id,
      spotNumber: parkingSpot.spotNumber,
      isAvailable: parkingSpot.isAvailable,
      type: parkingSpot.type,
    };
  });
  return <Table<DataType> columns={columns} dataSource={data} />;
};
