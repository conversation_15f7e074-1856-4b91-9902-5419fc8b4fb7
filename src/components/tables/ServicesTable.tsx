import { Table } from "antd";
import type { TableProps } from "antd";
import { Service } from "../../interfaces/service";

interface DataType {
  key: Service["id"];
  name: Service["name"];
  description: Service["description"];
  cost: Service["cost"];
}

interface ServicesTableProps {
  services: Service[];
}

export const ServicesTable: React.FC<ServicesTableProps> = ({ services }) => {
  const data: DataType[] = services.map((service) => {
    return {
      key: service.id,
      name: service.name,
      description: service.description,
      cost: service.cost,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Nombre",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Descripción",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Costo",
      dataIndex: "cost",
      key: "cost",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
