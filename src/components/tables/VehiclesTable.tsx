import { Table } from "antd";
import type { TableProps } from "antd";
import { Vehicle } from "../../interfaces/vehicle";
import { Actions } from "./table-elements/Actions";

interface DataType {
  key: Vehicle["id"];
  vehicle: Vehicle;
  plate: Vehicle["plate"];
  brand: Vehicle["brand"];
  model: Vehicle["model"];
  color: Vehicle["color"];
}

interface VehiclesTableProps {
  vehicles: Vehicle[];
  onEdit: (vehicle: Vehicle) => void;
  onDelete: (id: Vehicle["id"]) => void;
}

export const VehiclesTable: React.FC<VehiclesTableProps> = ({
  vehicles,
  onEdit,
  onDelete,
}) => {
  const data: DataType[] = vehicles.map((vehicle) => {
    return {
      key: vehicle.id,
      vehicle: vehicle,
      plate: vehicle.plate,
      brand: vehicle.brand,
      model: vehicle.model,
      color: vehicle.color,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Placa",
      dataIndex: "plate",
      key: "plate",
    },
    {
      title: "Marca",
      dataIndex: "brand",
      key: "brand",
    },
    {
      title: "Modelo",
      dataIndex: "model",
      key: "model",
    },
    {
      title: "Color",
      dataIndex: "color",
      key: "color",
    },
    {
      title: "Acciones",
      dataIndex: "vehicle",
      key: "vehicle",
      render: (vehicle: Vehicle) => {
        return (
          <Actions
            entity={vehicle}
            id={vehicle.id}
            onEdit={onEdit}
            onDelete={onDelete}
            url={vehicle.id}
          />
        );
      },
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
