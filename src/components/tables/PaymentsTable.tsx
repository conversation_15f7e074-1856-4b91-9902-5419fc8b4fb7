import { Table } from "antd";
import type { TableProps } from "antd";
import { Payment } from "../../interfaces/payment";

interface DataType {
  key: Payment["id"];
  amount: Payment["amount"];
  paymentDate: Payment["paymentDate"];
  description: Payment["description"];
}

interface PaymentsTableProps {
  payments: Payment[];
}

export const PaymentsTable: React.FC<PaymentsTableProps> = ({ payments }) => {
  const data: DataType[] = payments.map((payment) => {
    return {
      key: payment.id,
      amount: payment.amount,
      paymentDate: payment.paymentDate,
      description: payment.description,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Monto",
      dataIndex: "amount",
      key: "amount",
    },
    {
      title: "Fecha de pago",
      dataIndex: "paymentDate",
      key: "paymentDate",
    },
    {
      title: "Descripción",
      dataIndex: "description",
      key: "description",
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
