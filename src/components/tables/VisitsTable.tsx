import { Button, Table } from "antd";
import type { TableProps } from "antd";
import { Visit } from "../../interfaces/visit";
import { formatLongDateTime } from "../../utils.ts/time-utils";
import { useVisit } from "../../hooks/useCustomQueries";
import { Actions } from "./table-elements/Actions";

interface DataType {
  key: Visit["id"];
  visit: Visit;
  visitorName: Visit["visitorName"];
  vehiclePlate: Visit["vehiclePlate"];
  checkInTime: Visit["checkInTime"];
  schedule: Visit["checkInTime"];
  checkOutTime: Visit["checkOutTime"];
}

interface VisitsTableProps {
  visits: Visit[];
  onEdit?: (facility: Visit) => void;
  onDelete?: (id: Visit["id"]) => void;
}

export const VisitsTable: React.FC<VisitsTableProps> = ({
  visits,
  onEdit,
  onDelete,
}) => {
  const { checkOutMutation } = useVisit();

  const handleCheckOut = (visitId: string) => {
    checkOutMutation.mutate(visitId);
  };
  const data: DataType[] = visits.map((visit) => {
    return {
      key: visit.id,
      visit: visit,
      visitorName: visit.visitorName,
      vehiclePlate: visit.vehiclePlate,
      schedule: visit.schedule,
      checkInTime: visit.checkInTime,
      checkOutTime: visit.checkOutTime,
    };
  });
  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Nombre de visita",
      dataIndex: "visitorName",
      key: "visitorName",
    },
    {
      title: "Placa",
      dataIndex: "vehiclePlate",
      key: "vehiclePlate",
    },
    {
      title: "Programado",
      dataIndex: "schedule",
      key: "schedule",
      render: (schedule: Visit["schedule"]) => {
        return <p>{formatLongDateTime(schedule)}</p>;
      },
    },
    {
      title: "Check In",
      dataIndex: "checkInTime",
      key: "checkInTime",
      render: (checkInTime: Visit["checkInTime"]) => {
        return <p>{formatLongDateTime(checkInTime)}</p>;
      },
    },
    {
      title: "Check Out",
      dataIndex: "checkOutTime",
      key: "checkOutTime",
      sorter: (a, b) => {
        const dateA = a.checkOutTime ? new Date(a.checkOutTime).getTime() : 0;
        const dateB = b.checkOutTime ? new Date(b.checkOutTime).getTime() : 0;
        return dateA - dateB;
      },
      defaultSortOrder: "ascend",
      render: (checkOutTime: Visit["checkOutTime"], record) =>
        checkOutTime ? (
          formatLongDateTime(checkOutTime)
        ) : (
          <Button
            type="primary"
            onClick={() => handleCheckOut(record.key)}
            loading={checkOutMutation.isPending}
          >
            Hacer Check-out
          </Button>
        ),
    },
    {
      title: "Acciones",
      dataIndex: "visit",
      key: "visit",
      render: (visit: Visit) => (
        <Actions
          entity={visit}
          id={visit.id}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      ),
    },
  ];
  return <Table<DataType> columns={columns} dataSource={data} />;
};
