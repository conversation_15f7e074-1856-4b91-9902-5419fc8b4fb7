import { Button, Flex, Popconfirm } from "antd";
import { DeleteOutlined, EditOutlined, EyeOutlined } from "@ant-design/icons";
import { useNavigate } from "@tanstack/react-router";

interface ActionsProps<T> {
  entity?: T;
  onEdit?: (entity: T) => void;
  onDelete?: (id: string) => void;
  id?: string;
  url?: string;
}

export const Actions = <T,>({
  entity,
  onEdit,
  onDelete,
  id,
  url,
}: ActionsProps<T>) => {
  const navigate = useNavigate();

  return (
    <Flex gap={10}>
      {url && (
        <Button icon={<EyeOutlined />} onClick={() => navigate({ to: url })} />
      )}

      {entity && onEdit && (
        <Button icon={<EditOutlined />} onClick={() => onEdit?.(entity)} />
      )}

      {id && onDelete && (
        <Popconfirm
          title="¿Seguro que deseas eliminar este elemento?"
          onConfirm={() => onDelete?.(id)}
          okText="Sí"
          cancelText="No"
        >
          <Button danger icon={<DeleteOutlined />} />
        </Popconfirm>
      )}
    </Flex>
  );
};
