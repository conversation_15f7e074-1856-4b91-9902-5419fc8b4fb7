import React from "react";
import { DollarOutlined, StarOutlined, UserOutlined } from "@ant-design/icons";
import { User } from "../../interfaces/user";

interface ResidentIconProps {
  resident: User;
}

export const ResidentIcon: React.FC<ResidentIconProps> = ({ resident }) => {
  const residentRoles = resident.roles.map((role) => role.name);
  if (residentRoles.includes("OWNER")) {
    return <StarOutlined />;
  }

  if (residentRoles.includes("RENTAL")) {
    return <DollarOutlined />;
  }

  return <UserOutlined />;
};
