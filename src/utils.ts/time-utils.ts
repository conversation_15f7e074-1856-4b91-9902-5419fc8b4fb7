import dayjs from "dayjs";

export const longDateTimeFormat = "YYYY-MM-DD HH:mm:ss";
export const shortDateFormat = "DD-MM-YYYY";
export const dateFormat = "YYYY-MM-DD";

export const formatLongDateTime = (date: string | Date): string => {
  return dayjs(date).format(longDateTimeFormat);
};

export const formatShortDate = (date: string): string => {
  return dayjs(date).format(shortDateFormat);
};

export const todayDate = () => {
  return dayjs();
};

export const date = (date?: string) => {
  if (!date) {
    return date;
  }
  return dayjs(date);
};

export const months = [
  "Enero",
  "Febrero",
  "Marzo",
  "Abril",
  "Mayo",
  "Junio",
  "Julio",
  "Agosto",
  "Septiembre",
  "Octubre",
  "Noviembre",
  "Diciembre",
];

export const getMonthName = (monthNumber: number) => {
  // Validar que esté entre 1 y 12
  if (monthNumber < 1 || monthNumber > 12) {
    throw new Error("El número de mes debe estar entre 1 y 12.");
  }

  return months[monthNumber - 1];
};

export const getCurrentPeriod = () => {
  const now = new Date();
  return {
    month: now.getMonth() + 1,
    year: now.getFullYear(),
  };
};

export const daysOfWeekOptions = [
  { label: "Domingo", value: 0 },
  { label: "Lunes", value: 1 },
  { label: "Martes", value: 2 },
  { label: "Miércoles", value: 3 },
  { label: "Jueves", value: 4 },
  { label: "Viernes", value: 5 },
  { label: "Sábado", value: 6 },
];

export const getDayLabels = (dayNumbers: number[]) => {
  return dayNumbers
    .map((day) => daysOfWeekOptions.find((d) => d.value === day)?.label)
    .filter(Boolean);
};
