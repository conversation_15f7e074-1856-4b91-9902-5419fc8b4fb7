import { Property } from "./property";

export interface Infraction {
  id: string;
  description: string;
  date: string;
  severity: InfractionSeverity;
  propertyId: Property["id"];
  images: InfractionImage[];
}

export interface InfractionImage {
  id: string;
  path: string;
  infractionId: string;
  createdAt: Date;
}

export enum InfractionSeverity {
  MINOR = "MINOR",
  MODERATE = "MODERATE",
  SEVERE = "SEVERE",
}
