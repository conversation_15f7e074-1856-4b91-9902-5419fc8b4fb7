import { Property } from "./property";
import { User } from "./user";

export enum Status {
  OPEN = "OPEN",
  IN_PROGRESS = "IN_PROGRESS",
  RESOLVED = "RESOLVED",
}

export interface MaintenanceIssueReport {
  id: string;
  description: string;
  status: Status;
  createdAt: Date;
  updatedAt: Date;
  propertyId?: Property["id"];
  reportedBy: User["id"];
  images: MaintenanceIssueImage[];
}

export interface MaintenanceIssueImage {
  id: string;
  path: string;
  reportId: string;
  createdAt: Date;
}