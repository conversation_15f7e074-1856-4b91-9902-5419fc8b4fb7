import { Protocol } from "./protocol";
import { Regulation } from "./regulation";
import { Reservation } from "./reservation";

export interface Facility {
  id: string;
  name: string;
  description: string;
  startTime?: string;
  endTime?: string;
  imagePath: string;
  reservable: boolean;
  daysOfWeek: number[];
  open: string;
  close: string;
  updatedAt: string;
  createdAt: string;
  reservations: Reservation[];
  regulations: Regulation[];
  protocols: Protocol[];
  maxAmountOfPeople?: number;
  maxTimeOfStay?: number;
  maxDateOfReservation?: number;
}
