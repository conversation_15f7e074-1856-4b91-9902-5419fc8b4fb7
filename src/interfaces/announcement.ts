import { AnnouncementRecipient } from "./announcement-recipient";
import { Role } from "./role";

export interface Announcement {
  id: string;
  title: string;
  message: string;
  createdAt: Date;
  recipients: AnnouncementRecipient[];
  roleId: Role["id"];
  images: AnnouncementImage[];
}

export interface AnnouncementImage {
  id: string;
  path: string;
  announcementId: string;
  createdAt: Date;
}
