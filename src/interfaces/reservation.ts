import { Facility } from "./facility";
import { Property } from "./property";
import { User } from "./user";

export interface Reservation {
  id: string;
  amountOfPeople: number;
  startDateTime: Date;
  endDateTime: Date;
  status: ReservationStatus;
  createdAt: Date;
  updatedAt: Date;
  propertyId: Property["id"];
  facilityId: Facility["id"];
  requestedBy: User["id"];
  authorizedBy?: User["id"];
  denidedBy?: User["id"];
  authorizedAt?: Date;
  deniedAt?: Date;
  deniedReason?: string;
}

export enum ReservationStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}
