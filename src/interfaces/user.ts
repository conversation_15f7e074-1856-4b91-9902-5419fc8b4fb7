import { News } from "./news";
import { Property } from "./property";
import { Role } from "./role";
import { Tag } from "./tag";
import { Task } from "./task";
import { FacilityIssueReport } from "./maintenance-issue-report";
import { Complaint } from "./complaint";
import { AnnouncementRecipient } from "./announcement-recipient";

export interface User {
  id: string;
  email: string;
  firstName: string;
  paternalLastName: string;
  maternalLastName: string;
  phone: string;
  createdAt: Date;
  updatedAt: Date;
  announcementRecipient: AnnouncementRecipient[];
  complaints: Complaint[];
  events: Event[];
  facilityIssueReports: FacilityIssueReport[];
  newsItems: News[];
  properties: Property[];
  roles: Role[];
  tags: Tag[];
  tasks: Task[];
}
