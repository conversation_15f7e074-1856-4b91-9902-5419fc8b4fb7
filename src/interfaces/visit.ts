import { ParkingSpot } from "./parking-spot";
import { Property } from "./property";
import { User } from "./user";

export interface Visit {
  id: string;
  visitorName: string;
  requestedBy: User["id"];
  visitMethod: VisitMethod;
  vehiclePlate?: string;
  schedule: string;
  checkInTime: string;
  checkOutTime?: string;
  qrCode?: string;
  isQrUsed: boolean;
  propertyId: Property["id"];
  parkingSpotId?: ParkingSpot["id"];
}

export enum VisitMethod {
  WALKING = "WALKING",
  CAR = "CAR",
}
