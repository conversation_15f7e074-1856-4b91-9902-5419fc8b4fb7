import { User } from "./user";
import { ParkingSpot } from "./parking-spot";
import { Reservation } from "./reservation";
import { Rental } from "./rental";
import { Infraction } from "./infraction";
import { Payment } from "./payment";
import { Visit } from "./visit";
import { Pet } from "./pet";
import { Vehicle } from "./vehicle";
import { Tag } from "./tag";
import { MaintenanceIssueReport } from "./maintenance-issue-report";
import { Fine } from "./fine";
import { Complaint } from "./complaint";
import { Package } from "./packages";

export interface Property {
  id: string;
  ownerId: User["id"];
  address: string;
  type: PropertyType;

  createdAt: string;
  updatedAt: string;

  residents: User[];
  reservations: Reservation[];
  maintenanceIssueReports: MaintenanceIssueReport[];
  rentals: Rental[];
  infractions: Infraction[];
  payments: Payment[];
  visits: Visit[];
  parkingSpots: ParkingSpot[];
  pets: Pet[];
  vehicles: Vehicle[];
  tags: Tag[];
  fines: Fine[];
  complaints: Complaint[];
  packages: Package[];
}

export enum PropertyType {
  HOUSE = "HOUSE",
  DEPARTMENT = "DEPARTMENT",
}
