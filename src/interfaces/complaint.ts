import { ComplaintType } from "./complaint-type";
import { Status } from "./maintenance-issue-report";
import { Property } from "./property";
import { User } from "./user";

export interface Complaint {
  id: string;
  propertyId: Property["id"];
  userId: User["id"];
  complaintTypeId: ComplaintType["id"];

  detail: string;
  status: Status;
  priority: Priority;
  completedAt: string;
  createdAt: string;
  images: ComplaintImage[];
}

export interface ComplaintImage {
  id: string;
  path: string;
  complaintId: string;
  createdAt: Date;
}
export enum Priority {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
}
