import { Infraction } from "./infraction";
import { Property } from "./property";

export interface Fine {
  id: string;
  amount: number;
  description: string;
  issuedAt: string;
  isPaid: boolean;
  paidAt?: string;
  infractionId: Infraction["id"];
  propertyId: Property["id"];
  images: FineImage[];
}

export interface FineImage {
  id: string;
  path: string;
  fineId: string;
  createdAt: Date;
}
