import { Property } from "./property";
import { User } from "./user";

export enum PackageStatus {
  PENDING = "PENDING",
  DELIVERED = "DELIVERED",
  EXPIRED = "EXPIRED",
}

export interface Package {
  id: string;
  number: number;
  status: PackageStatus;
  deliveryToken?: string;
  tokenExpiresAt?: Date;
  receivedAt: Date;
  deliveredAt?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  propertyId: Property["id"];
  receivedBy: User["id"];
  deliveredBy?: User["id"];
  images: PackageImage[];
}

export interface PackageImage {
  id: string;
  path: string;
  packageId: string;
  createdAt: Date;
}

export interface PackageDeliveryToken {
  token: string;
  expiresAt: string;
}
