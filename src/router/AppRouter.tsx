import { DashboardLayout } from "../components";
import {
  create<PERSON>oot<PERSON>out<PERSON>,
  create<PERSON>out<PERSON>,
  create<PERSON><PERSON><PERSON>,
  Router<PERSON>rovider,
} from "@tanstack/react-router";
import { Roles } from "../pages/roles/Roles";
import { Reservations } from "../pages/reservations/Reservations";
import { Rentals } from "../pages/rentals/Rentals";
import { Regulations } from "../pages/regulations/Regulations";
import { Protocols } from "../pages/protocols/Protocols";
import { Properties } from "../pages/properties/Properties";
import { Payments } from "../pages/payments/Payments";
import { ParkingSpots } from "../pages/parking-spots/Parkingspots";
import { NotFound } from "../pages/not-found/NotFound";
import { News } from "../pages/news/News";
import { Infractions } from "../pages/infractions/Infractions";
import { Complaints } from "../pages/complaint/Complaints";
import { Home } from "../pages/home/<USER>";
import { HighLights } from "../pages/highlights/Highlights";
import { Fines } from "../pages/fines/Fines";
import { Facilities } from "../pages/facilities/Facilities";
import { Expenses } from "../pages/expenses/Expenses";
import { Events } from "../pages/events/Events";
import { Employees } from "../pages/employees/Employees";
import { ConfirmPassword } from "../pages/confirm-password/ConfirmPassword";
import { Auth } from "../pages/auth/Auth";
import { Announcements } from "../pages/announcements/Announcements";
import { Services } from "../pages/services/Services";
import { Suppliers } from "../pages/suppliers/Suppliers";
import { Tasks } from "../pages/tasks/Tasks";
import { Visits } from "../pages/visits/Visits";
import { Users } from "../pages/users/Users";
import { Property } from "../pages/properties/Property/Property";
import { Vehicles } from "../pages/vehicles/Vehicles";
import { Tags } from "../pages/tags/Tags";
import { Facility } from "../pages/facilities/facility/Facility";
import { ComplaintTypes } from "../pages/complaint-types/ComplaintTypes";
import { MaintenanceFees } from "../pages/maintenance-fees/MaintenanceFees";
import { MonthlyMaintenanceCharges } from "../pages/monthly-maintenance-charges/MonthlyMaintenanceCharges";
import { MaintenanceIssueReports } from "../pages/maintenance-issue-reports/MaintenanceIssueReports";
import { Phones } from "../pages/phone-directory/Phones";
import { Packages } from "../pages/packages/Packages";

export const rootRoute = createRootRoute();

export const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "dashboard",
  component: DashboardLayout,
});

export const rolesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "roles",
  component: Roles,
});

export const reservationsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "reservations",
  component: Reservations,
});

export const rentalsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "rentals",
  component: Rentals,
});

export const regulationsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "regulations",
  component: Regulations,
});

export const protocolsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "protocols",
  component: Protocols,
});

export const propertiesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "properties",
  component: Properties,
});

export const paymentsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "payments",
  component: Payments,
});

export const parkingSpotsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "parking-spots",
  component: ParkingSpots,
});

export const notFoundRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "*",
  component: NotFound,
});

export const newsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "news",
  component: News,
});

export const infractionsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "infractions",
  component: Infractions,
});

export const complaintsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "complaints",
  component: Complaints,
});

export const homeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: Home,
});

export const highlightsRotue = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "highlights",
  component: HighLights,
});

export const finesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "fines",
  component: Fines,
});

export const facilitiesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "facilities",
  component: Facilities,
});

export const expensesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "expenses",
  component: Expenses,
});

export const eventsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "events",
  component: Events,
});

export const employeesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "employees",
  component: Employees,
});

export const confirmPasswordRoutes = createRoute({
  getParentRoute: () => rootRoute,
  path: "confirm-password",
  component: ConfirmPassword,
});

export const authRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "auth",
  component: Auth,
});

export const announcementsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "announcements",
  component: Announcements,
});

export const servicesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "services",
  component: Services,
});

export const suppliersRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "suppliers",
  component: Suppliers,
});

export const tasksRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "tasks",
  component: Tasks,
});

export const vehiclesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "vehicles",
  component: Vehicles,
});

export const visitsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "visits",
  component: Visits,
});

export const tagsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "tags",
  component: Tags,
});

export const usersRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "users",
  component: Users,
});

export const complaintTypesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "complaint-types",
  component: ComplaintTypes,
});

export const MaintenanceFeesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "maintenance-fees",
  component: MaintenanceFees,
});

export const MonthlyMaintenanceChargesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "monthly-maintenance-charges",
  component: MonthlyMaintenanceCharges,
});

export const MaintenanceIssueReportsRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "maintenance-issue-reports",
  component: MaintenanceIssueReports,
});

export const packagesRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "packages",
  component: Packages,
});

export const phoneDirectoryRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "phone-directory",
  component: Phones,
});

//Singular

export const propertyRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "properties/$propertyId",
  component: Property,
});

export const facilityRoute = createRoute({
  getParentRoute: () => dashboardRoute,
  path: "facilities/$facilityId",
  component: Facility,
});

const routeTree = rootRoute.addChildren([
  dashboardRoute.addChildren([
    highlightsRotue,
    announcementsRoute,
    employeesRoute,
    eventsRoute,
    expensesRoute,
    facilitiesRoute,
    finesRoute,
    complaintsRoute,
    infractionsRoute,
    newsRoute,
    parkingSpotsRoute,
    paymentsRoute,
    propertiesRoute,
    protocolsRoute,
    regulationsRoute,
    reservationsRoute,
    rentalsRoute,
    servicesRoute,
    suppliersRoute,
    tasksRoute,
    usersRoute,
    visitsRoute,
    rolesRoute,
    vehiclesRoute,
    tagsRoute,
    complaintTypesRoute,
    MaintenanceFeesRoute,
    MonthlyMaintenanceChargesRoute,
    packagesRoute,
    //Single
    propertyRoute,
    facilityRoute,
    MaintenanceIssueReportsRoute,
    phoneDirectoryRoute,
  ]),
  notFoundRoute,
  homeRoute,
  confirmPasswordRoutes,
  authRoute,
]);

const router = createRouter({ routeTree });

export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};
