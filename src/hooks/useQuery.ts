import { useCrud } from "../hooks/useCrud";
import { Announcement } from "../interfaces/announcement";
import { <PERSON><PERSON>laint } from "../interfaces/complaint";
import { ComplaintType } from "../interfaces/complaint-type";
import { Employee } from "../interfaces/employee";
import { Event } from "../interfaces/event";
import { Expense } from "../interfaces/expense";
import { Facility } from "../interfaces/facility";
import { Fine } from "../interfaces/fine";
import { Infraction } from "../interfaces/infraction";
import { MaintenanceFee } from "../interfaces/maintenance-fee";
import { MaintenanceIssueReport } from "../interfaces/maintenance-issue-report";
import { MonthlyMaintenanceCharge } from "../interfaces/monthly-maintenance-charge";
import { News } from "../interfaces/news";
import { Package } from "../interfaces/packages";
import { ParkingSpot } from "../interfaces/parking-spot";
import { Payment } from "../interfaces/payment";
import { Pet } from "../interfaces/pet";
import { PhoneDirectory } from "../interfaces/phone-directory";
import { Property } from "../interfaces/property";
import { Protocol } from "../interfaces/protocol";
import { Regulation } from "../interfaces/regulation";
import { Rental } from "../interfaces/rental";
import { Reservation } from "../interfaces/reservation";
import { Role } from "../interfaces/role";
import { Service } from "../interfaces/service";
import { Step } from "../interfaces/step";
import { Supplier } from "../interfaces/supplier";
import { Tag } from "../interfaces/tag";
import { Task } from "../interfaces/task";
import { User } from "../interfaces/user";
import { Vehicle } from "../interfaces/vehicle";
import { Visit } from "../interfaces/visit";

// 📌 Centralizamos todos los hooks CRUD en un solo archivo
export const useAnnouncements = () => useCrud<Announcement>("announcement");
export const useComplaints = () => useCrud<Complaint>("complaint");
export const useComplaintTypes = () => useCrud<ComplaintType>("complaint-type");
export const useEmployees = () => useCrud<Employee>("employee");
export const useEvents = () => useCrud<Event>("event");
export const useExpenses = () => useCrud<Expense>("expense");
export const useFacilities = () => useCrud<Facility>("facility");
export const useMaintenanceIssueReports = () =>
  useCrud<MaintenanceIssueReport>("maintenance-issue-report");
export const useFines = () => useCrud<Fine>("fine");
export const useInfractions = () => useCrud<Infraction>("infraction");
export const useNews = () => useCrud<News>("news");
export const useParkingSpots = () => useCrud<ParkingSpot>("parking-spot");
export const usePayments = () => useCrud<Payment>("payment");
export const usePets = () => useCrud<Pet>("pet");
export const useProperties = () => useCrud<Property>("property");
export const useProtocols = () => useCrud<Protocol>("protocol");
export const useRegulations = () => useCrud<Regulation>("regulation");
export const useRentals = () => useCrud<Rental>("rental");
export const useReservations = () => useCrud<Reservation>("reservation");
export const useRoles = () => useCrud<Role>("role");
export const useServices = () => useCrud<Service>("service");
export const useSteps = () => useCrud<Step>("step");
export const useSuppliers = () => useCrud<Supplier>("supplier");
export const useTags = () => useCrud<Tag>("tag");
export const useTasks = () => useCrud<Task>("task");
export const useUsers = () => useCrud<User>("user");
export const useVehicles = () => useCrud<Vehicle>("vehicle");
export const useVisits = () => useCrud<Visit>("visit");
export const useMaintenanceFees = () =>
  useCrud<MaintenanceFee>("maintenance-fee");
export const useMonthlyMaintenanceCharges = () =>
  useCrud<MonthlyMaintenanceCharge>("monthly-maintenance-charge");
export const usePhoneDirectory = () =>
  useCrud<PhoneDirectory>("phone-directory");
export const usePackages = () => useCrud<Package>("package");
