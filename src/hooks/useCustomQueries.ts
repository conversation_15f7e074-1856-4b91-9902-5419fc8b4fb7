import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/api-clients";
import { ParkingSpot } from "../interfaces/parking-spot";
import { Visit } from "../interfaces/visit";
import { Property } from "../interfaces/property";
import { Regulation } from "../interfaces/regulation";
import { MonthlyMaintenanceCharge } from "../interfaces/monthly-maintenance-charge";
import { Facility } from "../interfaces/facility";
import { Reservation } from "../interfaces/reservation";
import { Package } from "../interfaces/packages";

export const useParkingSpots = () => {
  const visitorParkingSpotsQuery = useQuery({
    queryKey: ["visitor-parking-spots"],
    queryFn: async () => {
      const response = await hoaClient.get<ParkingSpot[]>(
        "/parking-spot/visitors"
      );
      return response.data;
    },
  });

  const propertyParkingSpotsQuery = (propertyId: Property["id"]) =>
    useQuery({
      queryKey: ["property-parking-spot", propertyId],
      queryFn: async ({ queryKey }) => {
        const [, id] = queryKey as [string, string]; // Extraer propertyId
        const response = await hoaClient.get<ParkingSpot[]>(
          `/parking-spot/by-property/${id}`
        );
        return response.data;
      },
      enabled: !!propertyId,
    });

  return { visitorParkingSpotsQuery, propertyParkingSpotsQuery };
};

export const useVisit = () => {
  const queryClient = useQueryClient();

  const checkOutMutation = useMutation({
    mutationFn: async (visitId: Visit["id"]) => {
      const response = await hoaClient.patch<ParkingSpot[]>(
        `/visit/${visitId}/checkout`
      );
      return response.data;
    },
    onSuccess: async () => {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ["visit"] }),
        queryClient.invalidateQueries({ queryKey: ["visitor-parking-spots"] }),
        queryClient.invalidateQueries({ queryKey: ["property-parking-spot"] }),
        queryClient.invalidateQueries({ queryKey: ["property"] }),
      ]);

      await Promise.all([
        queryClient.refetchQueries({ queryKey: ["visit"] }),
        queryClient.refetchQueries({ queryKey: ["visitor-parking-spots"] }),
        queryClient.refetchQueries({ queryKey: ["property-parking-spot"] }),
        queryClient.refetchQueries({ queryKey: ["property"] }),
      ]);
    },
  });

  return { checkOutMutation };
};

export const useCustomRegulation = () => {
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: async (data: FormData) => {
      const response = await hoaClient.post<Regulation>("/regulation", data, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["regulation"] });
      queryClient.refetchQueries({ queryKey: ["regulation"] });
    },
  });

  return { createMutation };
};

export const useCustomMonthlyMaintenanceCharge = () => {
  const queryClient = useQueryClient();

  const listBy = (month?: number, year?: number) => {
    const selectedMonth = month;
    const selectedYear = year;

    return useQuery({
      queryKey: ["monthly-maintenance-charges", selectedMonth, selectedYear],
      queryFn: async () => {
        const response = await hoaClient.get<MonthlyMaintenanceCharge[]>(
          `/monthly-maintenance-charge?month=${selectedMonth}&year=${selectedYear}`
        );
        return response.data;
      },
      staleTime: 1000 * 60 * 5, // 5 minutos
    });
  };

  const markAsPaidMutation = useMutation({
    mutationFn: async (
      monthlyMaintenanceChargeId: MonthlyMaintenanceCharge["id"]
    ) => {
      const response = await hoaClient.patch<ParkingSpot[]>(
        "/monthly-maintenance-charge/mark-as-paid",
        {
          id: monthlyMaintenanceChargeId, // 🔹 Se envía en el body
        }
      );
      return response.data;
    },
    onSuccess: async () => {
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: ["monthly-maintenance-charges"],
        }),
      ]);

      await Promise.all([
        queryClient.refetchQueries({
          queryKey: ["monthly-maintenance-charges"],
        }),
      ]);
    },
  });

  return { listBy, markAsPaidMutation };
};

export const useCustomFacitlity = () => {
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: async (data: FormData) => {
      const response = await hoaClient.post<Facility>("/facility", data, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["facility"] });
      queryClient.refetchQueries({ queryKey: ["facility"] });
    },
  });

  const updateMutation = useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: Facility["id"];
      data: FormData;
    }) => {
      const response = await hoaClient.patch<Facility>(
        `/facility/${id}`,
        data,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["facility"] });
      queryClient.refetchQueries({ queryKey: ["facility"] });
    },
  });

  return { createMutation, updateMutation };
};

//Authorize Reservation
export const useAuthorizeReservation = () => {
  const queryClient = useQueryClient();

  const authorizeMutation = useMutation({
    mutationFn: async (reservationId: Reservation["id"]) => {
      const response = await hoaClient.patch<Reservation>(
        `/reservation/${reservationId}/authorize`
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reservation"] });
      queryClient.refetchQueries({ queryKey: ["reservation"] });
    },
  });

  return { authorizeMutation };
};

export const useDenyReservation = () => {
  const queryClient = useQueryClient();

  const denyMutation = useMutation({
    mutationFn: async ({
      reservationId,
      deniedReason,
    }: {
      reservationId: Reservation["id"];
      deniedReason: string;
    }) => {
      const response = await hoaClient.patch<Reservation>(
        `/reservation/${reservationId}/deny`,
        {
          deniedReason: deniedReason,
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reservation"] });
      queryClient.refetchQueries({ queryKey: ["reservation"] });
    },
  });

  return { denyMutation };
};

export const useMarkPackageAsDelivered = () => {
  const queryClient = useQueryClient();

  const markAsDeliveredMutation = useMutation({
    mutationFn: async ({
      packageId,
      deliveryToken,
    }: {
      packageId: Package["id"];
      deliveryToken: string;
    }) => {
      const response = await hoaClient.patch<Package>(
        `/package/${packageId}/deliver`,
        {
          deliveryToken: deliveryToken,
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["package"] });
      queryClient.refetchQueries({ queryKey: ["package"] });
    },
  });

  return { markAsDeliveredMutation };
};
