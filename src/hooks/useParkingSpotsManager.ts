import { ParkingSpot, ParkingSpotType } from "../interfaces/parking-spot";
import { Property, PropertyType } from "../interfaces/property";

export const useParkingSpotsManger = (
  properties: Property[],
  parkingSpots: ParkingSpot[]
) => {
  const housesSortedByAddress = properties
    .filter((property) => property.type === PropertyType.HOUSE)
    .sort((a, b) => parseInt(a.address) - parseInt(b.address));

  const departmentsSortedByAddress = properties
    .filter((property) => property.type === PropertyType.DEPARTMENT)
    .sort((a, b) => parseInt(a.address) - parseInt(b.address));

  const parkinSpotsSortedByNumber = parkingSpots
    .filter((parkingSpot) => parkingSpot.type === ParkingSpotType.VISITOR)
    .sort(
      (a, b) =>
        parseInt(a.spotNumber.split("-")[1]) -
        parseInt(b.spotNumber.split("-")[1])
    );

  const houseRanges = [
    [1, 8],
    [9, 17],
    [18, 31],
    [32, 48],
    [49, 61],
    [62, 69],
    [70, 76],
    [77, 83],
    [84, 91],
    [92, 99],
    [100, 106],
    [107, 107],
    [108, 108],
    [109, 113],
    [114, 116],
    [117, 118],
    [119, 119],
  ];

  const houseBlocks = houseRanges.map(([start, end]) =>
    housesSortedByAddress.slice(start - 1, end)
  );

  const reversedHouseBlocks = houseBlocks.map((block) => [...block].reverse());

  const departmentBaseBlocks = [
    ["201", "200", "101A", "100A"],
    ["202", "300"],
    ["301", "302"],
    ["204", "203"],
    ["103A", "102A"],
    ["205", "303", "304", "305"],
  ];

  const departmentBlocks = departmentBaseBlocks.map(
    (block) =>
      block
        .map((address) =>
          departmentsSortedByAddress.find(
            (apartment) => apartment.address === address
          )
        )
        .filter(Boolean) as Property[]
  );

  const parkingSpotsRange = [
    [1, 6],
    [7, 7],
    [8, 8],
    [9, 14],
    [15, 15],
    [16, 17],
    [18, 19],
    [20, 21],
  ];

  const parkingSpotsBlocks = parkingSpotsRange.map(([start, end]) =>
    parkinSpotsSortedByNumber.slice(start - 1, end)
  );

  const reversedParkingSpotsBlocks = parkingSpotsBlocks.map((block) =>
    [...block].reverse()
  );

  return {
    houseBlocks,
    reversedHouseBlocks,
    parkingSpotsBlocks,
    reversedParkingSpotsBlocks,
    departmentBlocks,
  };
};
