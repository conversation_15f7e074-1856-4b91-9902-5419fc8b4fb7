import { useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/api-clients";

export const useCachedQuery = <T>(endpoint: string) => {
  const queryClient = useQueryClient();

  // Consultar si ya hay datos en caché
  const cachedData = queryClient.getQueryData<T[]>([endpoint]);

  // Definir la query, pero solo activarla si no hay datos en caché
  const query = useQuery({
    queryKey: [endpoint],
    queryFn: async () => {
      const response = await hoaClient.get<T[]>(`/${endpoint}`);
      return response.data;
    },
    enabled: !cachedData,
    staleTime: Infinity,
  });

  return {
    data: cachedData ?? query.data,
    isLoading: query.isLoading && !cachedData,
    isFetching: query.isFetching,
    refetch: query.refetch,
  };
};
