import { FormInstance } from "antd";
import { useEntityManager, UseEntityManagerProps } from "./useEntityManager";
import { mapImagesToUploadFile } from "../utils.ts/form-utils";

// Tipo para entidades que tienen un array de imágenes
export interface EntityWithImages {
  images: any[];
  [key: string]: any;
}

// Props para el hook useEntityManagerWithImages
export interface UseEntityManagerWithImagesProps<T extends EntityWithImages>
  extends UseEntityManagerProps<T> {
  // Opcionalmente podríamos agregar más props específicas para imágenes
}

// Tipo de retorno del hook
export interface UseEntityManagerWithImages<T extends EntityWithImages> {
  form: FormInstance<T>;
  isModalOpen: boolean;
  editingEntity: T | null;
  showModal: (entity?: T) => void;
  handleCancel: () => void;
  handleOk: () => Promise<void>;
  notificationContext: React.ReactNode;
}

/**
 * Hook personalizado que extiende useEntityManager para manejar entidades con imágenes
 * Convierte automáticamente las imágenes al formato UploadFile[] esperado por los componentes de formulario
 */
export const useEntityManagerWithImages = <T extends EntityWithImages>(
  options: UseEntityManagerWithImagesProps<T>
): UseEntityManagerWithImages<T> => {
  // Usamos el hook base
  const {
    form,
    isModalOpen,
    editingEntity,
    showModal: originalShowModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager<T>(options);

  // Función adaptadora para manejar correctamente entidades con imágenes
  const showModal = (entity?: T) => {
    if (entity && entity.images) {
      // Convertir las imágenes al formato esperado por el formulario
      const entityWithUploadFiles = {
        ...entity,
        images: mapImagesToUploadFile(entity.images),
      };
      originalShowModal(entityWithUploadFiles);
    } else {
      originalShowModal(entity);
    }
  };

  return {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  };
};
