import { useState } from "react";
import { Form, notification } from "antd";
import { FormInstance } from "antd/es/form";
import { date } from "../utils.ts/time-utils";

export interface UseEntityManagerProps<T> {
  createEntity: (data: T) => Promise<void> | void;
  updateEntity: (data: T) => Promise<void> | void;
  initialValues?: RecursivePartial<T>;
}

type RecursivePartial<T> = NonNullable<T> extends object
  ? {
      [P in keyof T]?: NonNullable<T[P]> extends (infer U)[]
        ? RecursivePartial<U>[]
        : NonNullable<T[P]> extends object
        ? RecursivePartial<T[P]>
        : T[P];
    }
  : T;

interface UseEntityManager<T> {
  form: FormInstance<T>;
  isModalOpen: boolean;
  editingEntity: T | null;
  showModal: (entity?: RecursivePartial<T>) => void;
  handleCancel: () => void;
  handleOk: () => Promise<void>;
  notificationContext: React.ReactNode;
}

export type EntityType = "Visit";

const dateFields = [
  "schedule",
  "checkInTime",
  "checkOutTime",
  "date",
  "hireDate",
  "issuedAt",
  "startDate",
  "endDate",
  "open",
  "close",
  "startTime",
  "endTime",
  "dueDate",
  "completedAt",
  "startDateTime",
  "endDateTime",
];

export const useEntityManager = <T>({
  createEntity,
  updateEntity,
}: UseEntityManagerProps<T>): UseEntityManager<T> => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [editingEntity, setEditingEntity] = useState<T | null>(null);
  const [form] = Form.useForm<T>();

  const [api, contextHolder] = notification.useNotification();

  const showModal = (entity?: RecursivePartial<T>) => {
    setEditingEntity((entity ?? null) as T | null);
    form.resetFields();

    if (entity) {
      const formattedEntity: Record<string, any> = { ...entity };

      for (const field of dateFields) {
        const value = (entity as any)[field];
        if (value) {
          formattedEntity[field] = date(value); // transforma en dayjs
        }
      }

      form.setFieldsValue(formattedEntity);
    }

    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingEntity) {
        await Promise.resolve(updateEntity(values));
      } else {
        await Promise.resolve(createEntity(values));
      }
      api.success({
        message: editingEntity
          ? "Entidad actualizada con éxito"
          : "Entidad creada con éxito",
      });

      setIsModalOpen(false);
    } catch (error) {
      console.error("Validation failed:", error);
      api.error({
        message: "Error al guardar la entidad",
        description: "Ocurrió un error al intentar guardar los cambios.",
      });
    }
  };

  return {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext: contextHolder,
  };
};
