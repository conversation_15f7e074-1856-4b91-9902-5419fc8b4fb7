import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/api-clients";
import { MaybeWithImages } from "./types";

export const useCrud = <T extends { id: string }>(endpoint: string) => {
  const queryClient = useQueryClient();

  // Obtener todos los registros
  const listQuery = useQuery({
    queryKey: [endpoint],
    queryFn: async () => {
      const response = await hoaClient.get<T[]>(`/${endpoint}`);
      return response.data;
    },
    staleTime: 1000 * 60 * 5, // Mantiene el caché fresco por 5 minutos
    refetchOnWindowFocus: false,
    initialData: undefined, // No usa datos en caché para la respuesta del hook
  });

  // Obtener un solo registro por ID
  const getByIdQuery = (id: string) =>
    useQuery({
      queryKey: [endpoint, id],
      queryFn: async () => {
        const response = await hoaClient.get<T>(`/${endpoint}/${id}`);
        return response.data;
      },
      enabled: !!id, // Solo ejecuta la query si hay un ID
    });

  // ✅ Función para invalidar y refrescar datos
  const refreshQueries = () => {
    queryClient.invalidateQueries({ queryKey: [endpoint] });
    queryClient.refetchQueries({ queryKey: [endpoint] });
  };

  const invalidateQueries = (id?: string) => {
    queryClient.invalidateQueries({ queryKey: [endpoint] });
    if (id) {
      queryClient.invalidateQueries({ queryKey: [endpoint, id] });
    }
  };

  // Crear un nuevo registro
  const createMutation = useMutation({
    mutationFn: async (data: MaybeWithImages<Omit<T, "id">>) => {
      let response;

      const hasNewFiles = data.images?.some((img) => img.originFileObj);

      if (hasNewFiles) {
        const formData = new FormData();

        data.images?.forEach((image) => {
          if (image.originFileObj) {
            formData.append("files", image.originFileObj);
          }
        });

        // Agregar el array completo de imágenes como JSON
        formData.append("images", JSON.stringify(data.images));

        Object.entries(data).forEach(([key, value]) => {
          if (
            key !== "images" &&
            key !== "files" &&
            value !== undefined &&
            value !== null
          ) {
            formData.append(key, value as unknown as string);
          }
        });

        response = await hoaClient.post<T>(`/${endpoint}`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      } else {
        response = await hoaClient.post<T>(`/${endpoint}`, data);
      }

      return response.data;
    },
    onSuccess: refreshQueries,
  });

  // Actualizar un registro existente
  const updateMutation = useMutation({
    mutationFn: async (data: MaybeWithImages<T> & { id: string }) => {
      let response;

      if (data.images && data.images.length > 0) {
        const formData = new FormData();

        data.images.forEach((image) => {
          if (image.originFileObj) {
            formData.append("files", image.originFileObj);
          }
        });

        // Serializar el array completo para mantener referencia a las existentes
        formData.append("images", JSON.stringify(data.images));

        Object.entries(data).forEach(([key, value]) => {
          if (
            key !== "images" &&
            key !== "files" &&
            key !== "id" &&
            value !== undefined &&
            value !== null
          ) {
            formData.append(key, value as string);
          }
        });

        response = await hoaClient.patch<T>(
          `/${endpoint}/${data.id}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
      } else {
        response = await hoaClient.patch<T>(`/${endpoint}/${data.id}`, data);
      }

      return response.data;
    },
    onSuccess: (_data, variables) => {
      invalidateQueries(variables.id); // 👈 variables contiene el objeto con el id
    },
  });

  // Eliminar un registro
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      await hoaClient.delete(`/${endpoint}/${id}`);
    },
    onSuccess: refreshQueries,
  });

  return {
    listQuery,
    getByIdQuery,
    createMutation,
    updateMutation,
    deleteMutation,
  };
};
