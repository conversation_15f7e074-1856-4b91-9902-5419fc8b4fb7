import { DashboardContentHeader } from "../../components";
import { PaymentForm } from "../../components/forms/PaymentForm";
import { EntityTable } from "../../components/base/EntityTable";
import { useEntityManager } from "../../hooks/useEntityManager";
import { usePayments } from "../../hooks/useQuery";
import { Payment } from "../../interfaces/payment";
import { EntityModal } from "../../components/base/EntityModal";

export const Payments: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    usePayments();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Payment) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Payment) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Pagos"
        tooltipTitle="Registra un pago"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(payment) => ({
          ...payment,
          key: payment.id,
        })}
        baseColumns={[
          {
            title: "Monto",
            dataIndex: "amount",
            key: "amount",
            render: (amount) => `$${amount.toLocaleString()}`,
          },
          {
            title: "Fecha de pago",
            dataIndex: "paymentDate",
            key: "paymentDate",
            render: (paymentDate) => new Date(paymentDate).toLocaleDateString(),
          },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Creado el",
            dataIndex: "createdAt",
            key: "createdAt",
            render: (createdAt) => new Date(createdAt).toLocaleDateString(),
          },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Pago"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <PaymentForm form={form} />
      </EntityModal>
    </>
  );
};
