import { DashboardContentHeader } from "../../components";
import { TaskForm } from "../../components/forms/TaskForm";
import { EntityTable } from "../../components/base/EntityTable";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useTasks } from "../../hooks/useQuery";
import { Task } from "../../interfaces/task";
import { EntityModal } from "../../components/base/EntityModal";
import { getTaskStatusLabel } from "../../utils.ts/form-utils";

export const Tasks: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useTasks();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Task) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Task) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Tareas"
        tooltipTitle="Crea una tarea"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(task) => ({
          ...task,
          key: task.id,
        })}
        baseColumns={[
          { title: "Título", dataIndex: "title", key: "title" },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Estado",
            dataIndex: "status",
            key: "status",
            render: (status) => getTaskStatusLabel(status),
          },
          {
            title: "Fecha límite",
            dataIndex: "dueDate",
            key: "dueDate",
            render: (dueDate) => new Date(dueDate).toLocaleDateString(),
          },
          {
            title: "Completado en",
            dataIndex: "completedAt",
            key: "completedAt",
            render: (completedAt) =>
              completedAt ? new Date(completedAt).toLocaleDateString() : "-",
          },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Tarea"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <TaskForm form={form} />
      </EntityModal>
    </>
  );
};
