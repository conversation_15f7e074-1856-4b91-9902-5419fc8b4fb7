import { DashboardContentHeader } from "../../components";
import { ExpenseForm } from "../../components/forms/ExpenseForm";
import { EntityTable } from "../../components/base/EntityTable";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useExpenses } from "../../hooks/useQuery";
import { Expense } from "../../interfaces/expense";
import { EntityModal } from "../../components/base/EntityModal";

export const Expenses: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useExpenses();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Expense) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Expense) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Gastos"
        tooltipTitle="Registra un gasto"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(expense) => ({
          ...expense,
          key: expense.id,
        })}
        baseColumns={[
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Monto",
            dataIndex: "amount",
            key: "amount",
            render: (amount) => `$${amount.toLocaleString()}`,
          },
          {
            title: "Fecha",
            dataIndex: "date",
            key: "date",
            render: (date) => new Date(date).toLocaleDateString(),
          },
          { title: "Recibo/Factura", dataIndex: "receipt", key: "receipt" },
          {
            title: "Creado el",
            dataIndex: "createdAt",
            key: "createdAt",
            render: (createdAt) => new Date(createdAt).toLocaleDateString(),
          },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Gasto"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <ExpenseForm form={form} />
      </EntityModal>
    </>
  );
};
