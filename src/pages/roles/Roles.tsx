import { Modal, Typography } from "antd";
import { Dashboard<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RoleForm, RolesList } from "../../components";
import { useEntityManager } from "../../hooks/useEntityManager";
import { Role } from "../../interfaces/role";
import { useRoles } from "../../hooks/useQuery";

const { Title } = Typography;

export const Roles: React.FC = () => {
  const { listQuery, createMutation, updateMutation } = useRoles();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Role) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Role) => {
      updateMutation.mutateAsync(data);
    },
  });

  if (listQuery.isLoading) return <p>Cargando roles...</p>;
  if (listQuery.isError) return <p>Error: {listQuery.error.message}</p>;

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Roles"
        tooltipTitle="Crea un rol"
        onClick={() => showModal()}
      />

      <RolesList roles={listQuery.data ?? []} onEdit={showModal} />

      <Modal
        title={
          <Title level={4}>{editingEntity ? "Editar Rol" : "Crear Rol"}</Title>
        }
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <RoleForm form={form} />
      </Modal>
    </>
  );
};
