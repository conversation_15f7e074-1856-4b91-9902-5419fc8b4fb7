import { DashboardContentHeader } from "../../components";
import { PropertiesTable } from "../../components/tables";
import { useProperties } from "../../hooks/useQuery";

export const Properties: React.FC = () => {
  const { listQuery } = useProperties();
  return (
    <>
      <DashboardContentHeader title="Propiedades" />

      <PropertiesTable properties={listQuery.data ?? []} />
    </>
  );
};
