import { useParams } from "@tanstack/react-router";
import { useProperties } from "../../../hooks/useQuery";
import { Users, Vehicles } from "../../../components";
import {
  ComplaintsTable,
  InfractionsTable,
  MaintenanceIssueReportsTable,
  PaymentsTable,
  RentalsTable,
  ReservationsTable,
  VisitsTable,
} from "../../../components/tables";

export const Property: React.FC = () => {
  const { propertyId } = useParams({ strict: false });
  const { getByIdQuery } = useProperties();

  if (!getByIdQuery) {
    return <p>Error: No se pudo obtener la propiedad.</p>; // 🔹 Manejo seguro si `getQuery` es `null`
  }

  const { data: property, isLoading, isError } = getByIdQuery(propertyId);

  if (isLoading) return <p>Cargando propiedad...</p>;
  if (isError || !property) return <p>No se pudo cargar la propiedad.</p>;

  const {
    residents,
    vehicles,
    complaints,
    infractions,
    address,
    visits,
    payments,
    parkingSpots,
    pets,
    rentals,
    reservations,
    tags,
    maintenanceIssueReports,
  } = property;

  return (
    <div>
      <h2>Propiedad {address ?? ""}</h2>
      <h4>Residentes: {residents?.length ?? 0}</h4>
      <Users users={residents} />
      <h4>Vehiculos: {vehicles?.length ?? 0}</h4>
      <Vehicles vehicles={vehicles} />
      {vehicles.map((vehicle) => (
        <>
          <p>{vehicle.plate}</p>
          <p>{vehicle.model}</p>
        </>
      ))}
      <h4>Mascotas {pets?.length ?? 0}</h4>
      {pets.map((pet) => (
        <>
          <p>{pet.name}</p>
          <p>{pet.type}</p>
        </>
      ))}
      <h4>Lugares de estacionamiento {parkingSpots?.length ?? 0}</h4>
      {parkingSpots.map((parkingSpot) => (
        <p key={parkingSpot.id}>{parkingSpot.spotNumber}</p>
      ))}
      <h4>Tags {tags?.length ?? 0}</h4>
      <h4>Infracciones: {infractions?.length ?? 0}</h4>
      <InfractionsTable infractions={infractions} />
      <h4>Visitas: {visits?.length ?? 0}</h4>
      <VisitsTable visits={visits} />
      <h4>Quejas {complaints?.length ?? 0}</h4>
      <ComplaintsTable complaints={complaints} />
      <h4>Pagos {payments?.length ?? 0}</h4>
      <PaymentsTable payments={payments} />
      <h4>Rentas {rentals?.length ?? 0}</h4>
      <RentalsTable rentals={rentals} />
      <h4>Reservaciones {reservations?.length ?? 0}</h4>
      <ReservationsTable reservations={reservations} />
      <h4>Reportes de Amenidades {maintenanceIssueReports?.length ?? 0}</h4>
      <MaintenanceIssueReportsTable maintenanceIssueReports={maintenanceIssueReports} />
    </div>
  );
};
