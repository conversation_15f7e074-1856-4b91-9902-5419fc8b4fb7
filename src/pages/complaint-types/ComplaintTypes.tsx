import { Modal, Typography } from "antd";
import { ComplaintTypesTable, DashboardContentHeader } from "../../components";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useComplaintTypes } from "../../hooks/useQuery";
import { ComplaintType } from "../../interfaces/complaint-type";
import { ComplaintTypeForm } from "../../components/forms/ComplaintTypeForm";

const { Title } = Typography;

export const ComplaintTypes: React.FC = () => {
  const { listQuery, createMutation, updateMutation } = useComplaintTypes();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: ComplaintType) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: ComplaintType) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Tipo de quejas"
        tooltipTitle="Registrar tipo de queja"
        onClick={() => showModal()}
      />
      <ComplaintTypesTable
        complaintTypes={listQuery.data ?? []}
        onEdit={showModal}
      />
      <Modal
        title={
          <Title level={4}>
            {editingEntity ? "Editar tipo de queja" : "Crear tipo de queja"}
          </Title>
        }
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <ComplaintTypeForm form={form} />
      </Modal>
    </>
  );
};
