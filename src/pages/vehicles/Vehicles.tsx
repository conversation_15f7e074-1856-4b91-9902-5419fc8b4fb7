import { DashboardContentHeader } from "../../components";
import { VehicleForm } from "../../components/forms/VehicleForm";
import { EntityTable } from "../../components/base/EntityTable";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useVehicles } from "../../hooks/useQuery";
import { Vehicle } from "../../interfaces/vehicle";
import { EntityModal } from "../../components/base/EntityModal";

export const Vehicles: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useVehicles();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Vehicle) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Vehicle) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Vehículos"
        tooltipTitle="Registra un vehículo"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(vehicle) => ({
          ...vehicle,
          key: vehicle.id,
        })}
        baseColumns={[
          { title: "Placa", dataIndex: "plate", key: "plate" },
          { title: "Marca", dataIndex: "brand", key: "brand" },
          { title: "Modelo", dataIndex: "model", key: "model" },
          { title: "Color", dataIndex: "color", key: "color" },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Vehículo"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <VehicleForm form={form} />
      </EntityModal>
    </>
  );
};
