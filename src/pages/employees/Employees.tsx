import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { DashboardContentHeader } from "../../components/dashboard/DashboardContentHeader";
import { EmployeeForm } from "../../components/forms/EmployeeForm";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useEmployees } from "../../hooks/useQuery";
import { Employee } from "../../interfaces/employee";

export const Employees: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useEmployees();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Employee) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Employee) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Empleados"
        tooltipTitle="Registra un empleado"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(employee) => ({
          ...employee,
          key: employee.id,
          employee: employee,
        })}
        baseColumns={[
          {
            title: "Nombre",
            dataIndex: "employee",
            key: "employee",
            render: (employee: Employee) => (
              <p>{`${employee.firstName} ${employee.maternalLastName} ${employee.paternalLastName}`}</p>
            ),
          },
          {
            title: "Cargo",
            dataIndex: "position",
            key: "position",
          },
          {
            title: "Teléfono",
            dataIndex: "phone",
            key: "phone",
          },
          {
            title: "Correo",
            dataIndex: "email",
            key: "email",
          },
          {
            title: "Fecha de contratación",
            dataIndex: "hireDate",
            key: "hireDate",
          },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Empleado"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <EmployeeForm form={form} />
      </EntityModal>
    </>
  );
};
