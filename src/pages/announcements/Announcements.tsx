import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { AnnouncementForm } from "../../components/forms/AnnouncementForm";
import { MaybeWithImages } from "../../hooks/types";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useAnnouncements } from "../../hooks/useQuery";
import { Announcement } from "../../interfaces/announcement";

export const Announcements: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useAnnouncements();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: MaybeWithImages<Omit<Announcement, "id">>) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: MaybeWithImages<Announcement> & { id: string }) => {
      updateMutation.mutateAsync(data);
    },
  });
  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Comunicados"
        tooltipTitle="Crear comunicado"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(announcement) => ({
          ...announcement,
          key: announcement.id,
        })}
        baseColumns={[
          {
            title: "Título",
            dataIndex: "title",
            key: "title",
          },
          {
            title: "Mensaje",
            dataIndex: "message",
            key: "message",
          },
          {
            title: "Creado el",
            dataIndex: "createdAt",
            key: "createdAt",
          },
        ]}
        onEdit={(item) => {
          const adaptedItem = {
            ...item,
            images: item.images || [],
          };
          showModal(adaptedItem as any);
        }}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Comunicado"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <AnnouncementForm form={form} />
      </EntityModal>
    </>
  );
};
