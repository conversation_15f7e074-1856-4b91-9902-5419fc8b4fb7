import { DashboardContentHeader } from "../../components";
import { ComplaintsTable } from "../../components/tables/ComplaintsTable";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useComplaints } from "../../hooks/useQuery";
import { Complaint } from "../../interfaces/complaint";
import { ComplaintForm } from "../../components/forms/ComplaintForm";
import { EntityModal } from "../../components/base/EntityModal";
import { MaybeWithImages } from "../../hooks/types";

export const Complaints: React.FC = () => {
  const { listQuery, createMutation, updateMutation } = useComplaints();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: MaybeWithImages<Omit<Complaint, "id">>) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: MaybeWithImages<Complaint> & { id: string }) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Quejas"
        tooltipTitle="Registrar queja"
        onClick={() => showModal()}
      />
      <ComplaintsTable
        complaints={listQuery.data ?? []}
        onEdit={(item) => {
          const adaptedItem = {
            ...item,
            images: item.images || [],
          };
          showModal(adaptedItem as any);
        }}
      />
      <EntityModal
        title="Queja"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <ComplaintForm form={form} />
      </EntityModal>
    </>
  );
};
