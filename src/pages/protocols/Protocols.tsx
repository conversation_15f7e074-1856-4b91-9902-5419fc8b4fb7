import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { ProtocolForm } from "../../components/forms/ProtocolForm";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useProtocols } from "../../hooks/useQuery";
import { Protocol } from "../../interfaces/protocol";
import { Step } from "../../interfaces/step";

export const Protocols: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useProtocols();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Protocol) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Protocol) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Protocolos"
        tooltipTitle="Crear protocolo"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(protocol) => ({
          ...protocol,
          key: protocol.id,
        })}
        baseColumns={[
          {
            title: "Título",
            dataIndex: "title",
            key: "title",
          },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Amenidad",
            dataIndex: "facilityId",
            key: "facilityId",
          },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Protocolo"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <ProtocolForm form={form} />
      </EntityModal>
    </>
  );
};
