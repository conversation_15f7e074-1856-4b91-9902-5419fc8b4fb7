import { Card, Flex, List, Statistic, Typography } from "antd";

const { Text } = Typography;

const upcomingEvents = [
  {
    title: "Reunión de propietarios",
    date: "15 de Mayo, 2024",
    description: "Discusión del presupuesto anual",
  },
  {
    title: "Mantenimiento de piscina",
    date: "20 de Mayo, 2024",
    description: "Cierre temporal de la piscina",
  },
  {
    title: "Día de limpieza comunitaria",
    date: "1 de Junio, 2024",
    description: "Voluntarios necesarios",
  },
  {
    title: "Fiesta de verano",
    date: "15 de Julio, 2024",
    description: "Barbacoa y actividades para niños",
  },
];

export const HighLights: React.FC = () => {
  return (
    <>
      <Flex
        style={{
          display: "flex",
          gap: "16px",
          marginBottom: "16px",
          flexWrap: "wrap",
        }}
      >
        <Card style={{ flex: 1, minWidth: "200px" }}>
          <Statistic
            title="Saldo actual"
            value={50000}
            prefix="$"
            precision={2}
          />
        </Card>
        <Card style={{ flex: 1, minWidth: "200px" }}>
          <Statistic title="Cuotas pendientes" value={5} suffix="/ 100" />
        </Card>
        <Card style={{ flex: 1, minWidth: "200px" }}>
          <Statistic title="Solicitudes de mantenimiento" value={12} />
        </Card>
      </Flex>
      <Flex style={{ display: "flex", gap: "16px", flexWrap: "wrap" }}>
        <Card title="Próximos eventos" style={{ flex: 1, minWidth: "300px" }}>
          <List
            itemLayout="horizontal"
            dataSource={upcomingEvents.slice(0, 3)}
            renderItem={(item) => (
              <List.Item>
                <List.Item.Meta
                  title={item.title}
                  description={
                    <>
                      <Text>{item.date}</Text>
                      <br />
                      <Text type="secondary">{item.description}</Text>
                    </>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      </Flex>
    </>
  );
};
