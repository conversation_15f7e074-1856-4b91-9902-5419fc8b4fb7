import { useState } from "react";
import {
  Layout,
  <PERSON>u,
  Ava<PERSON>,
  Card,
  Stati<PERSON>,
  List,
  But<PERSON>,
  Typo<PERSON>,
  Badge,
  Tabs,
  Modal,
  Form,
  Input,
  DatePicker,
} from "antd";
import {
  DashboardOutlined,
  CalendarOutlined,
  ToolOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { TitleInput } from "../../components/form-elements/inputs";

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

export const DecondaryDashboard: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState("1");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState("");

  const menuItems = [
    { key: "1", icon: <DashboardOutlined />, label: "Dashboard" },
    { key: "2", icon: <CalendarOutlined />, label: "Eventos" },
    { key: "3", icon: <ToolOutlined />, label: "Mantenimiento" },
    { key: "4", icon: <BellOutlined />, label: "Anuncios" },
  ];

  const upcomingEvents = [
    {
      title: "Reunión de propietarios",
      date: "15 de Mayo, 2024",
      description: "Discusión del presupuesto anual",
    },
    {
      title: "Mantenimiento de piscina",
      date: "20 de Mayo, 2024",
      description: "Cierre temporal de la piscina",
    },
    {
      title: "Día de limpieza comunitaria",
      date: "1 de Junio, 2024",
      description: "Voluntarios necesarios",
    },
    {
      title: "Fiesta de verano",
      date: "15 de Julio, 2024",
      description: "Barbacoa y actividades para niños",
    },
  ];

  const announcements = [
    {
      title: "Nuevo sistema de seguridad",
      date: "10 de Mayo, 2024",
      content: "Se instalarán cámaras adicionales en las áreas comunes.",
    },
    {
      title: "Actualización de las reglas de estacionamiento",
      date: "5 de Mayo, 2024",
      content:
        "Por favor, revise las nuevas normas de estacionamiento para visitantes.",
    },
    {
      title: "Corte de agua programado",
      date: "20 de Mayo, 2024",
      content:
        "Habrá un corte de agua el 25 de mayo de 8:00 AM a 12:00 PM para mantenimiento.",
    },
  ];

  const maintenanceRequests = [
    { title: "Reparación de ascensor", status: "En progreso" },
    { title: "Fuga en el techo del edificio B", status: "Pendiente" },
    { title: "Reemplazo de luces en el estacionamiento", status: "Completado" },
  ];

  const showModal = (type: any) => {
    setModalType(type);
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleSubmit = (values: any) => {
    console.log("Submitted:", values);
    setIsModalVisible(false);
  };

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <Sider
        collapsible
        collapsed={collapsed}
        onCollapse={setCollapsed}
        theme="light"
      >
        <div
          style={{
            height: 32,
            margin: 16,
            background: "#237A91",
            borderRadius: 4,
          }}
        />
        <Menu
          theme="light"
          defaultSelectedKeys={["1"]}
          mode="inline"
          items={menuItems}
          onClick={({ key }) => setActiveTab(key)}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: 0, background: "#fff" }}>
          <div style={{ float: "right", marginRight: 24 }}>
            <Avatar icon={<UserOutlined />} />
            <span style={{ marginLeft: 8, marginRight: 8 }}>John Doe</span>
            <Button type="link" icon={<LogoutOutlined />}>
              Salir
            </Button>
          </div>
        </Header>
        <Content style={{ margin: "16px" }}>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="Dashboard" key="1">
              <Title level={2}>Dashboard HOA</Title>
              <div
                style={{
                  display: "flex",
                  gap: "16px",
                  marginBottom: "16px",
                  flexWrap: "wrap",
                }}
              >
                <Card style={{ flex: 1, minWidth: "200px" }}>
                  <Statistic
                    title="Saldo actual"
                    value={50000}
                    prefix="$"
                    precision={2}
                  />
                </Card>
                <Card style={{ flex: 1, minWidth: "200px" }}>
                  <Statistic
                    title="Cuotas pendientes"
                    value={5}
                    suffix="/ 100"
                  />
                </Card>
                <Card style={{ flex: 1, minWidth: "200px" }}>
                  <Statistic title="Solicitudes de mantenimiento" value={12} />
                </Card>
              </div>
              <div style={{ display: "flex", gap: "16px", flexWrap: "wrap" }}>
                <Card
                  title="Próximos eventos"
                  extra={
                    <Button type="link" onClick={() => showModal("event")}>
                      Añadir
                    </Button>
                  }
                  style={{ flex: 1, minWidth: "300px" }}
                >
                  <List
                    itemLayout="horizontal"
                    dataSource={upcomingEvents.slice(0, 3)}
                    renderItem={(item) => (
                      <List.Item>
                        <List.Item.Meta
                          title={item.title}
                          description={
                            <>
                              <Text>{item.date}</Text>
                              <br />
                              <Text type="secondary">{item.description}</Text>
                            </>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>
                <Card
                  title="Solicitudes de mantenimiento"
                  style={{ flex: 1, minWidth: "300px" }}
                >
                  <List
                    itemLayout="horizontal"
                    dataSource={maintenanceRequests}
                    renderItem={(item) => (
                      <List.Item>
                        <List.Item.Meta
                          title={item.title}
                          description={
                            <Badge
                              status={
                                item.status === "Completado"
                                  ? "success"
                                  : item.status === "En progreso"
                                  ? "processing"
                                  : "warning"
                              }
                              text={item.status}
                            />
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>
              </div>
            </TabPane>
            <TabPane tab="Eventos" key="2">
              <Title level={2}>Eventos</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => showModal("event")}
                style={{ marginBottom: 16 }}
              >
                Nuevo Evento
              </Button>
              <List
                itemLayout="vertical"
                dataSource={upcomingEvents}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={item.title}
                      description={item.date}
                    />
                    {item.description}
                  </List.Item>
                )}
              />
            </TabPane>
            <TabPane tab="Mantenimiento" key="3">
              <Title level={2}>Solicitudes de Mantenimiento</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => showModal("maintenance")}
                style={{ marginBottom: 16 }}
              >
                Nueva Solicitud
              </Button>
              <List
                itemLayout="horizontal"
                dataSource={maintenanceRequests}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={item.title}
                      description={
                        <Badge
                          status={
                            item.status === "Completado"
                              ? "success"
                              : item.status === "En progreso"
                              ? "processing"
                              : "warning"
                          }
                          text={item.status}
                        />
                      }
                    />
                  </List.Item>
                )}
              />
            </TabPane>
            <TabPane tab="Anuncios" key="4">
              <Title level={2}>Anuncios</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => showModal("announcement")}
                style={{ marginBottom: 16 }}
              >
                Nuevo Anuncio
              </Button>
              <List
                itemLayout="vertical"
                dataSource={announcements}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={item.title}
                      description={item.date}
                    />
                    {item.content}
                  </List.Item>
                )}
              />
            </TabPane>
          </Tabs>
        </Content>
      </Layout>

      <Modal
        title={
          modalType === "event"
            ? "Nuevo Evento"
            : modalType === "announcement"
            ? "Nuevo Anuncio"
            : "Nueva Solicitud de Mantenimiento"
        }
        onCancel={handleCancel}
        footer={null}
      >
        <Form onFinish={handleSubmit} layout="vertical">
          <TitleInput />
          {modalType === "event" && (
            <Form.Item name="date" label="Fecha" rules={[{ required: true }]}>
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          )}
          <Form.Item
            name="description"
            label="Descripción"
            rules={[{ required: true }]}
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              Guardar
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};
