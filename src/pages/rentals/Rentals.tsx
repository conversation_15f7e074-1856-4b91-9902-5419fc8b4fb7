import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { RentalForm } from "../../components/forms/RentalForm";
import { RentalsTable } from "../../components/tables/RentalsTable";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useRentals } from "../../hooks/useQuery";
import { Rental } from "../../interfaces/rental";

export const Rentals: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useRentals();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Rental) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Rental) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Rentas"
        tooltipTitle="Registra una renta"
        onClick={() => showModal()}
      />

      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(rental) => ({
          ...rental,
          key: rental.id,
        })}
        baseColumns={[
          { title: "Inicio", dataIndex: "startDate", key: "startDate" },
          { title: "Final", dataIndex: "endDate", key: "endDate" },
          { title: "Monto", dataIndex: "monthlyRate", key: "monthlyRate" },
          { title: "Status", dataIndex: "status", key: "status" },
          { title: "Propiedad", dataIndex: "propertyId", key: "propertyId" },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />

      <EntityModal
        title="Renta"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <RentalForm form={form} />
      </EntityModal>
    </>
  );
};
