import { DashboardContentHeader } from "../../components";
import { useMaintenanceIssueReports } from "../../hooks/useQuery";
import { MaintenanceIssueReport } from "../../interfaces/maintenance-issue-report";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { MaintenanceIssueReportForm } from "../../components/forms/MaintenanceIssueReportForm";
import { MaybeWithImages } from "../../hooks/types";
import { useEntityManagerWithImages } from "../../hooks/useEntityManagerWithImages";

export const MaintenanceIssueReports: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useMaintenanceIssueReports();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManagerWithImages({
    createEntity: (
      data: MaybeWithImages<Omit<MaintenanceIssueReport, "id">>
    ) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (
      data: MaybeWithImages<MaintenanceIssueReport> & { id: string }
    ) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Reportes de Mantenimiento"
        tooltipTitle="Registra un problema de mantenimiento"
        onClick={() => showModal()}
      />

      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(maintenanceIssueReport) => ({
          ...maintenanceIssueReport,
          key: maintenanceIssueReport.id,
        })}
        baseColumns={[
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Estado",
            dataIndex: "status",
            key: "status",
            render: (status) => {
              switch (status) {
                case "OPEN":
                  return "Abierto";
                case "IN_PROGRESS":
                  return "En progreso";
                case "RESOLVED":
                  return "Resuelto";
                default:
                  return "Desconocido";
              }
            },
          },
          {
            title: "Fecha de creación",
            dataIndex: "createdAt",
            key: "createdAt",
            render: (createdAt) => new Date(createdAt).toLocaleString(),
          },
          {
            title: "Última actualización",
            dataIndex: "updatedAt",
            key: "updatedAt",
            render: (updatedAt) => new Date(updatedAt).toLocaleString(),
          },
        ]}
        onEdit={(item) => {
          const adaptedItem = {
            ...item,
            images: item.images || [],
          };
          showModal(adaptedItem as any);
        }}
        onDelete={deleteMutation.mutate}
      />

      <EntityModal
        title="Reporte de mantenimiento"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <MaintenanceIssueReportForm form={form} />
      </EntityModal>
    </>
  );
};
