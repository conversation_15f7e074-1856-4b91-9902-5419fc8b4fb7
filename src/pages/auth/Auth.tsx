import { Col, Row } from "antd";
import { AuthForm } from "../../components";
import Title from "antd/es/typography/Title";
import logo from "../../assets/sabino.jpeg";
import { AuthRequest } from "../../interfaces/auth";
import { authenticate } from "../../api/auth";

const contentStyle: React.CSSProperties = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: "transparent",
  width: "100vw",
};

export const Auth: React.FC = () => {
  const onSubmit = async (formData: AuthRequest) => {
    await authenticate(formData);
  };

  return (
    <Row style={contentStyle}>
      <Col xs={16} sm={16} md={16} lg={8} xl={8}>
        <img src={logo} alt="" style={{ maxWidth: "80%" }} />
      </Col>
      <Col xs={16} sm={16} md={16} lg={8} xl={8}>
        <Title level={1}>Bienvenido</Title>
        <AuthForm onSubmit={onSubmit} />
      </Col>
    </Row>
  );
};
