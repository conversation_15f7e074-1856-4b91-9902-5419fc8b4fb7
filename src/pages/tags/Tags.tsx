import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { TagForm } from "../../components/forms/TagForm";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useTags } from "../../hooks/useQuery";
import { Tag } from "../../interfaces/tag";

export const Tags: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useTags();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Tag) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Tag) => {
      updateMutation.mutateAsync(data);
    },
  });
  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Tags"
        tooltipTitle="Registra un tag"
        onClick={() => showModal()}
      />

      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(tag) => ({
          ...tag,
          key: tag.id,
        })}
        baseColumns={[
          { title: "Nombre", dataIndex: "name", key: "name" },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          { title: "Propiedad", dataIndex: "propertyId", key: "propertyId" },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />

      <EntityModal
        title="Tag"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <TagForm form={form} />
      </EntityModal>
    </>
  );
};
