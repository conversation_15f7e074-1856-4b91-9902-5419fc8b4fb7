import { Modal, Typography } from "antd";
import { DashboardContentHeader, VisitsTable } from "../../components";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useVisits } from "../../hooks/useQuery";
import { Visit } from "../../interfaces/visit";
import { VisitForm } from "../../components/forms/VisitForm";
import { VisitParkingSpotsMap } from "../../components/maps";
import { useQueryClient } from "@tanstack/react-query";

const { Title } = Typography;

export const Visits: React.FC = () => {
  const queryClient = useQueryClient();
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useVisits();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Visit) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Visit) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Visitas"
        tooltipTitle="Registrar una visita"
        onClick={() => showModal()}
      />
      <VisitParkingSpotsMap />
      <VisitsTable
        visits={listQuery.data ?? []}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <Modal
        title={
          <Title level={4}>
            {editingEntity ? "Editar Visita" : "Crear Visita"}
          </Title>
        }
        open={isModalOpen}
        onOk={async () => {
          handleOk();
          await Promise.all([
            queryClient.invalidateQueries({
              queryKey: ["visitor-parking-spots"],
            }),
            queryClient.invalidateQueries({
              queryKey: ["property-parking-spot"],
            }),
            queryClient.invalidateQueries({ queryKey: ["property"] }),
          ]);

          await Promise.all([
            queryClient.refetchQueries({ queryKey: ["visitor-parking-spots"] }),
            queryClient.refetchQueries({ queryKey: ["property-parking-spot"] }),
            queryClient.refetchQueries({ queryKey: ["property"] }),
          ]);
        }}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <VisitForm form={form} />
      </Modal>
    </>
  );
};
