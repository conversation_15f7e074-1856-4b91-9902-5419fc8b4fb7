import { DashboardContentHeader } from "../../components";
import { InfractionsTable } from "../../components/tables/InfractionsTable";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useInfractions } from "../../hooks/useQuery";
import { Infraction } from "../../interfaces/infraction";
import { InfractionForm } from "../../components/forms/InfractionForm";
import { EntityModal } from "../../components/base/EntityModal";
import { MaybeWithImages } from "../../hooks/types";

export const Infractions: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useInfractions();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: MaybeWithImages<Omit<Infraction, "id">>) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: MaybeWithImages<Infraction> & { id: string }) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Incumplimientos"
        tooltipTitle="Registra un incumplimiento"
        onClick={() => showModal()}
      />
      <InfractionsTable
        infractions={listQuery.data ?? []}
        onEdit={(item) => {
          const adaptedItem = {
            ...item,
            images: item.images || [],
          };
          showModal(adaptedItem as any);
        }}
        onDelete={deleteMutation.mutate}
      />

      <EntityModal
        title="Incumplimiento"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <InfractionForm form={form} />
      </EntityModal>
    </>
  );
};
