import { useState } from "react";
import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { useCustomMonthlyMaintenanceCharge } from "../../hooks/useCustomQueries";
import { useEntityManager } from "../../hooks/useEntityManager";
import {
  useMaintenanceFees,
  useMonthlyMaintenanceCharges,
} from "../../hooks/useQuery";
import { MonthlyMaintenanceCharge } from "../../interfaces/monthly-maintenance-charge";
import { Property } from "../../interfaces/property";
import { getCurrentPeriod, getMonthName } from "../../utils.ts/time-utils";
import { MonthyMaintenanceChargesSearchForm } from "../../components/forms/MonthyMaintenanceChargesSearchForm";
import { Button } from "antd";
import { DollarOutlined } from "@ant-design/icons";

export const MonthlyMaintenanceCharges: React.FC = () => {
  const { createMutation, updateMutation } = useMonthlyMaintenanceCharges();

  const { month: defaultMonth, year: defaultYear } = getCurrentPeriod();

  const [year, setYear] = useState<number | undefined>(defaultYear);
  const [month, setMonth] = useState<number | undefined>(defaultMonth);

  const { listBy, markAsPaidMutation } = useCustomMonthlyMaintenanceCharge();

  const maintanenceCharges = listBy(month, year);

  const { listQuery } = useMaintenanceFees();

  const {
    isModalOpen,
    editingEntity,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: MonthlyMaintenanceCharge) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: MonthlyMaintenanceCharge) => {
      updateMutation.mutateAsync(data);
    },
  });
  return (
    <>
      {notificationContext}
      <DashboardContentHeader title="Cuota de mantenimiento" />
      <MonthyMaintenanceChargesSearchForm
        defaultMonth={defaultMonth}
        defaultYear={defaultYear}
        existingMaintenanceFee={listQuery.data ?? []}
        onSearch={(selectedMonth, selectedYear) => {
          setMonth(selectedMonth);
          setYear(selectedYear);
        }}
      />
      {month && year && (
        <p style={{ marginTop: 16, textAlign: "right" }}>
          Mostrando resultados de <strong>{getMonthName(month)}</strong> del año{" "}
          <strong>{year}</strong>
        </p>
      )}
      <EntityTable
        data={maintanenceCharges.data ?? []}
        mapToRow={(monthlyMaintenanceCharge) => ({
          ...monthlyMaintenanceCharge,
          key: monthlyMaintenanceCharge.id,
          id: monthlyMaintenanceCharge.id,
        })}
        baseColumns={[
          {
            title: "Propiedad",
            dataIndex: "property",
            key: "property",
            render: (property: Property) => <p>{property.address}</p>,
          },
          {
            title: "Mes",
            dataIndex: "month",
            key: "month",
            render: (month: MonthlyMaintenanceCharge["month"]) => (
              <p>{getMonthName(month)}</p>
            ),
          },
          {
            title: "Año",
            dataIndex: "year",
            key: "year",
          },
          {
            title: "Pagado",
            dataIndex: "isPaid",
            key: "isPaid",
            render: (isPaid: MonthlyMaintenanceCharge["isPaid"]) => (
              <p>{isPaid ? "Pagado" : "No pagado"}</p>
            ),
          },
          {
            title: "Recargos aplicados",
            dataIndex: "waivedLateFee",
            key: "waivedLateFee",
            render: (
              waivedLateFee: MonthlyMaintenanceCharge["waivedLateFee"]
            ) => <p>{waivedLateFee ? "Si" : "No"}</p>,
          },
          {
            title: "Pagar",
            dataIndex: "id",
            key: "id",
            render: (id: MonthlyMaintenanceCharge["id"]) => (
              <Button
                icon={<DollarOutlined />}
                onClick={() => markAsPaidMutation.mutate(id)}
              />
            ),
          },
        ]}
      />
      <EntityModal
        title="Cuota"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <></>
        {/* <MaintenanceFeeForm form={form} /> */}
      </EntityModal>
    </>
  );
};
