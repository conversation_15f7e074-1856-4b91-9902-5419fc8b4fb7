import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { ServiceForm } from "../../components/forms/ServiceForm";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useServices } from "../../hooks/useQuery";
import { Service } from "../../interfaces/service";

export const Services: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useServices();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Service) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Service) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Servicios"
        tooltipTitle="Registra un servicio"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(service) => ({
          ...service,
          key: service.id,
        })}
        baseColumns={[
          { title: "Name", dataIndex: "name", key: "name" },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          { title: "Proveedor", dataIndex: "supplierId", key: "supplierId" },
          { title: "Costo", dataIndex: "cost", key: "cost" },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Servicio"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <ServiceForm form={form} />
      </EntityModal>
    </>
  );
};
