import { DashboardContentHeader, UserForm, UsersTable } from "../../components";
import { Modal, Typography } from "antd";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useUsers } from "../../hooks/useQuery";
import { User } from "../../interfaces/user";

const { Title } = Typography;

export const Users: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useUsers();
  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: User) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: User) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Usuarios"
        tooltipTitle="Registra un usuario"
        onClick={() => showModal()}
      />
      <UsersTable
        users={listQuery.data ?? []}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <Modal
        title={
          <Title level={4}>
            {editingEntity !== null ? "Editar Usuario" : "Crear Usuario"}
          </Title>
        }
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <UserForm form={form} user={editingEntity} />
      </Modal>
    </>
  );
};
