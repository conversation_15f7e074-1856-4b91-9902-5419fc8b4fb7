import { DashboardContentHeader, ParkingSpotsTable } from "../../components";
import { VisitParkingSpotsMap } from "../../components/maps";
import { useParkingSpots } from "../../hooks/useQuery";

export const ParkingSpots: React.FC = () => {
  const { listQuery } = useParkingSpots();
  return (
    <>
      <DashboardContentHeader title="Estacionamiento" />
      <VisitParkingSpotsMap parkingSpots={listQuery.data ?? []} />
      <ParkingSpotsTable parkingSpots={listQuery.data ?? []} />
    </>
  );
};
