import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { EventForm } from "../../components/forms/EventForm";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useEvents } from "../../hooks/useQuery";
import { Event } from "../../interfaces/event";

export const Events: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useEvents();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Event) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Event) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Eventos"
        tooltipTitle="Crea un evento"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(event) => ({
          ...event,
          key: event.id,
        })}
        baseColumns={[
          {
            title: "Título",
            dataIndex: "title",
            key: "title",
          },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Ubicación",
            dataIndex: "location",
            key: "location",
          },
          {
            title: "Inicia",
            dataIndex: "startDate",
            key: "startDate",
          },
          {
            title: "Termina",
            dataIndex: "endDate",
            key: "endDate",
          },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />

      <EntityModal
        title="Evento"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <EventForm form={form} />
      </EntityModal>
    </>
  );
};
