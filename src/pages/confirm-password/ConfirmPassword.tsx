import { ConfirmPasswordForm } from "../../components";
import { confirmPassword } from "../../api/auth";
import { PasswordConfirmationValues } from "../../interfaces/auth";
import { useSearch } from "@tanstack/react-router";
import { confirmPasswordRoutes } from "../../router/AppRouter";

export const ConfirmPassword: React.FC = () => {
  const { token } = useSearch({ from: confirmPasswordRoutes.id }); // 🔹 Aquí recuperas el token de la query string

  const onSubmit = async (formData: PasswordConfirmationValues) => {
    if (token) {
      await confirmPassword({
        ...formData,
        token,
      });
    }
  };

  return <ConfirmPasswordForm onSubmit={onSubmit} />;
};
