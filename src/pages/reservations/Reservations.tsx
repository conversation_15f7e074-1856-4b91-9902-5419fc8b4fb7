import { DashboardContentHeader } from "../../components";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useReservations } from "../../hooks/useQuery";
import { Reservation } from "../../interfaces/reservation";
import { ReservationForm } from "../../components/forms/ReservationForm";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { formatLongDateTime } from "../../utils.ts/time-utils";
import {
  useAuthorizeReservation,
  useDenyReservation,
} from "../../hooks/useCustomQueries";
import { Button, Input, Popover } from "antd";
import { useState } from "react";

export const Reservations: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useReservations();

  const { authorizeMutation } = useAuthorizeReservation();
  const { denyMutation } = useDenyReservation();
  const [reason, setReason] = useState("");

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Reservation) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Reservation) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Reservaciones"
        tooltipTitle="Registra una reservación"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(reservation) => ({
          ...reservation,
          key: reservation.id,
        })}
        baseColumns={[
          {
            title: "#",
            dataIndex: "amountOfPeople",
            key: "amountOfPeople",
          },
          {
            title: "Inicia",
            dataIndex: "startDateTime",
            key: "startDateTime",
            render: (startDateTime: Reservation["startDateTime"]) => {
              return <p>{formatLongDateTime(startDateTime)}</p>;
            },
          },
          {
            title: "Termina",
            dataIndex: "endDateTime",
            key: "endDateTime",
            render: (endDateTime: Reservation["endDateTime"]) => {
              return <p>{formatLongDateTime(endDateTime)}</p>;
            },
          },
          {
            title: "Status",
            dataIndex: "status",
            key: "status",
          },
          {
            title: "Solicitado por",
            dataIndex: "requestedBy",
            key: "requestedBy",
          },
          {
            title: "Aprobado",
            key: "reservation",
            render: (_value, record) => {
              if (record.authorizedAt) {
                return (
                  <p>
                    {record.authorizedBy} - {record.authorizedAt}
                  </p>
                );
              }
              return (
                <Button onClick={() => authorizeMutation.mutate(record.id)}>
                  Aprobar
                </Button>
              );
            },
          },
          {
            title: "Rechazado por",
            key: "denidedBy",
            render: (_value, record) => {
              if (record.deniedAt) {
                return (
                  <p>
                    {record.denidedBy} - {record.deniedAt}
                  </p>
                );
              }
              return (
                <Popover
                  title="Motivo del rechazo"
                  trigger="click"
                  content={
                    <div style={{ maxWidth: 200 }}>
                      <Input.TextArea
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        rows={3}
                      />
                      <Button
                        type="primary"
                        onClick={() =>
                          denyMutation.mutate({
                            reservationId: record.id,
                            deniedReason: reason,
                          })
                        }
                        style={{ marginTop: 8 }}
                        block
                      >
                        Rechazar
                      </Button>
                    </div>
                  }
                >
                  <Button danger size="small">
                    Rechazar
                  </Button>
                </Popover>
              );
            },
          },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Reservación"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <ReservationForm form={form} />
      </EntityModal>
    </>
  );
};
