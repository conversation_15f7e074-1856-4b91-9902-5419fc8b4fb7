import { useParams } from "@tanstack/react-router";
import { useFacilities } from "../../../hooks/useQuery";
import {
  RegulationsTable,
  ReservationsTable,
} from "../../../components";
import { EntityTable } from "../../../components/base/EntityTable";

export const Facility: React.FC = () => {
  const { facilityId } = useParams({ strict: false });
  const { getByIdQuery } = useFacilities();
  if (!getByIdQuery) {
    return <p>Error: No se pudo obtener la amenidad.</p>;
  }

  const { data: facility, isLoading, isError } = getByIdQuery(facilityId);

  if (isLoading) return <p>Cargando propiedad...</p>;
  if (isError || !facility) return <p>No se pudo cargar la propiedad.</p>;

  const {
    name,
    description,
    reservations,
    regulations,
    protocols,
  } = facility;

  return (
    <>
      <h2>{name ?? ""}</h2>
      <h4>{description}</h4>
      <br />
      <h4>Reservaciones</h4>
      <ReservationsTable reservations={reservations} />
      <br />
      <h4>Reglamentos</h4>
      <RegulationsTable regulations={regulations} />
      <br />
      <h4>Protocolos</h4>
      <EntityTable
        data={protocols}
        mapToRow={(protocol) => ({
          ...protocol,
          key: protocol.id,
        })}
        baseColumns={[
          {
            title: "Título",
            dataIndex: "title",
            key: "title",
          },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Amenidad",
            dataIndex: "facilityId",
            key: "facilityId",
          },
        ]}
      />
    </>
  );
};
