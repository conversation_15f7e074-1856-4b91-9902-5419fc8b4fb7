import { DashboardContentHeader, FacilityForm } from "../../components";
import { useEntityManager } from "../../hooks/useEntityManager";
import { Facility } from "../../interfaces/facility";
import { useFacilities } from "../../hooks/useQuery";
import { EntityTable } from "../../components/base/EntityTable";
import { EntityModal } from "../../components/base/EntityModal";
import dayjs from "dayjs";
import { getDayLabels } from "../../utils.ts/time-utils";

export const Facilities: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useFacilities();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Facility) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Facility) => {
      updateMutation.mutateAsync(data);
    },
  });
  if (listQuery.isLoading) return <p>Cargando amenidades...</p>;
  if (listQuery.isError) return <p>Error: {listQuery.error.message}</p>;

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Amenidades"
        tooltipTitle="Crea amenidad"
        onClick={() => showModal()}
      />

      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(facility) => ({
          ...facility,
          key: facility.id,
        })}
        baseColumns={[
          { title: "Nombre", dataIndex: "name", key: "name" },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Abre",
            dataIndex: "open",
            key: "open",
            render: (open) => {
              return dayjs(open).format("HH:MM");
            },
          },
          {
            title: "Cierra",
            dataIndex: "close",
            key: "close",
            render: (close) => {
              return dayjs(close).format("HH:MM");
            },
          },
          {
            title: "Reserva desde",
            dataIndex: "startTime",
            key: "startTime",
            render: (startTime) => {
              return startTime ? dayjs(startTime).format("HH:MM") : "N/R";
            },
          },
          {
            title: "Reserva hasta",
            dataIndex: "endTime",
            key: "endTime",
            render: (endTime) => {
              return endTime ? dayjs(endTime).format("HH:MM") : "N/R";
            },
          },
          {
            title: "Días disponibles",
            dataIndex: "daysOfWeek",
            key: "daysOfWeek",
            render: (daysOfWeek) => {
              return daysOfWeek.length ? `${getDayLabels(daysOfWeek)}` : "N/R";
            },
          },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />

      <EntityModal
        title="Amenidad"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={() => form.submit()}
        handleCancel={handleCancel}
      >
        <FacilityForm
          form={form}
          handleCancel={handleCancel}
          initialValues={editingEntity ?? undefined}
        />
      </EntityModal>
    </>
  );
};
