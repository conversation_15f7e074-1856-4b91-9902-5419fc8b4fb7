import { DashboardContentHeader } from "../../components";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useFines } from "../../hooks/useQuery";
import { Fine } from "../../interfaces/fine";
import { FineForm } from "../../components/forms/FineForm";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { MaybeWithImages } from "../../hooks/types";

export const Fines: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useFines();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: MaybeWithImages<Omit<Fine, "id">>) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: MaybeWithImages<Fine> & { id: string }) => {
      updateMutation.mutateAsync(data);
    },
  });
  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Multas"
        tooltipTitle="Crea una multa"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(fine) => ({
          ...fine,
          key: fine.id,
        })}
        baseColumns={[
          {
            title: "Monto",
            dataIndex: "amount",
            key: "amount",
          },
          {
            title: "Descripción",
            dataIndex: "description",
            key: "description",
          },
          {
            title: "Emitido el",
            dataIndex: "issuedAt",
            key: "issuedAt",
          },
          {
            title: "Pagado",
            dataIndex: "isPaid",
            key: "isPaid",
            render: (isPaid: boolean) => {
              return <p>{isPaid ? "Pagado" : "No pagado"}</p>;
            },
          },
        ]}
        onEdit={(item) => {
          const adaptedItem = {
            ...item,
            images: item.images || [],
          };
          showModal(adaptedItem as any);
        }}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Multa"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <FineForm form={form} />
      </EntityModal>
    </>
  );
};
