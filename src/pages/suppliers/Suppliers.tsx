import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { SupplierForm } from "../../components/forms/SupplierForm";
import { SuppliersTable } from "../../components/tables/SuppliersTable";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useSuppliers } from "../../hooks/useQuery";
import { Supplier } from "../../interfaces/supplier";

export const Suppliers: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useSuppliers();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Supplier) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Supplier) => {
      updateMutation.mutateAsync(data);
    },
  });
  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Proveedores"
        tooltipTitle="Registra un proveedor"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(supplier) => ({
          ...supplier,
          key: supplier.id,
        })}
        baseColumns={[
          { title: "Name", dataIndex: "name", key: "name" },
          { title: "Teléfono", dataIndex: "phone", key: "phone" },
          { title: "Correo", dataIndex: "email", key: "email" },
          { title: "Dirección", dataIndex: "address", key: "address" },
          { title: "Empleados", dataIndex: "employees", key: "employees" },
          { title: "Servicios", dataIndex: "service", key: "service" },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Proveedor"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <SupplierForm form={form} />
      </EntityModal>
    </>
  );
};
