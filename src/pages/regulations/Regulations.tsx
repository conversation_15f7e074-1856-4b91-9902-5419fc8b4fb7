import { Modal, Typography } from "antd";
import { DashboardContentHeader } from "../../components";
import { RegulationsTable } from "../../components/tables/RegulationsTable";
import { useRegulations } from "../../hooks/useQuery";
import { Regulation } from "../../interfaces/regulation";
import { useEntityManager } from "../../hooks/useEntityManager";
import { RegulationForm } from "../../components/forms/RegulationForm";

const { Title } = Typography;

export const Regulations: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    useRegulations();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: Regulation) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: Regulation) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Reglamentos"
        tooltipTitle="Registra un reglamento"
        onClick={() => showModal()}
      />
      <RegulationsTable
        regulations={listQuery.data ?? []}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <Modal
        title={
          <Title level={4}>
            {editingEntity ? "Editar Reglamento" : "Crear Reglamento"}
          </Title>
        }
        open={isModalOpen}
        onOk={() => form.submit()}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <RegulationForm form={form} handleCancel={handleCancel} />
      </Modal>
    </>
  );
};
