import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { MaintenanceFeeForm } from "../../components/forms/MaintenanceFeeForm";
import { useEntityManager } from "../../hooks/useEntityManager";
import { useMaintenanceFees } from "../../hooks/useQuery";
import { MaintenanceFee } from "../../interfaces/maintenance-fee";

export const MaintenanceFees: React.FC = () => {
  const { listQuery, createMutation, updateMutation } = useMaintenanceFees();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: MaintenanceFee) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: MaintenanceFee) => {
      updateMutation.mutateAsync(data);
    },
  });
  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Cuota de mantenimiento"
        tooltipTitle="Crea una cuota"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(fine) => ({
          ...fine,
          key: fine.id,
        })}
        baseColumns={[
          {
            title: "Monto",
            dataIndex: "amount",
            key: "amount",
            render: (amount: MaintenanceFee["amount"]) => (
              <p>$ {amount.toLocaleString()}</p>
            ),
          },
          {
            title: "Año",
            dataIndex: "year",
            key: "year",
          },
        ]}
      />
      <EntityModal
        title="Cuota"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <MaintenanceFeeForm form={form} />
      </EntityModal>
    </>
  );
};
