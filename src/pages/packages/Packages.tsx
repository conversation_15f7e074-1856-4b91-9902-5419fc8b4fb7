import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { PackageForm } from "../../components/forms/PackageForm";
import { useEntityManager } from "../../hooks/useEntityManager";
import { usePackages } from "../../hooks/useQuery";
import { Package } from "../../interfaces/packages";
import { MaybeWithImages } from "../../hooks/types";
import { Button, Form } from "antd";
import { DeliveredProcedureOutlined } from "@ant-design/icons";
import { useMarkPackageAsDelivered } from "../../hooks/useCustomQueries";
import { useState } from "react";
import { PackageTokenForm } from "../../components/forms/PackageTokenForm";

export const Packages: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    usePackages();

  const { markAsDeliveredMutation } = useMarkPackageAsDelivered();

  const [isDeliveryModalOpen, setIsDeliveryModalOpen] = useState(false);
  const [selectedPackageId, setSelectedPackageId] = useState<string>();

  const handleDelivery = (packageId?: Package["id"]) => {
    if (!packageId) return;
    const deliveryToken = deliveryForm?.getFieldValue("deliveryToken");
    markAsDeliveredMutation.mutate({ packageId, deliveryToken });
  };

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: MaybeWithImages<Omit<Package, "id">>) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: MaybeWithImages<Package> & { id: string }) => {
      updateMutation.mutateAsync(data);
    },
  });

  const [deliveryForm] = Form.useForm();

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Paquetes"
        tooltipTitle="Crea un paquete"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(packageInfo) => ({
          ...packageInfo,
          key: packageInfo.id,
        })}
        baseColumns={[
          { title: "Número", dataIndex: "number", key: "number" },
          { title: "Status", dataIndex: "status", key: "status" },
          { title: "Creado el", dataIndex: "createdAt", key: "createdAt" },
          { title: "Propiedad", dataIndex: "propertyId", key: "propertyId" },
          { title: "Recibido por", dataIndex: "receivedBy", key: "receivedBy" },
          {
            title: "Entregado por",
            dataIndex: "deliveredBy",
            key: "deliveredBy",
          },
          { title: "Notas", dataIndex: "notes", key: "notes" },
          // Change the next to a button to trigger modal
          {
            title: "Entregar",
            dataIndex: "id",
            key: "id",
            render: (id: Package["id"]) => (
              <Button
                icon={<DeliveredProcedureOutlined />}
                onClick={() => {
                  setIsDeliveryModalOpen(true);
                  setSelectedPackageId(id);
                }}
              />
            ),
          },
        ]}
        onEdit={(item) => {
          const adaptedItem = {
            ...item,
            images: item.images || [],
          };
          showModal(adaptedItem as any);
        }}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Paquete"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <PackageForm form={form} />
      </EntityModal>
      <EntityModal
        title="Entregar paquete"
        isModalOpen={isDeliveryModalOpen}
        isEditMode={false}
        handleOk={() => handleDelivery(selectedPackageId)}
        handleCancel={() => setIsDeliveryModalOpen(false)}
      >
        <PackageTokenForm form={deliveryForm} />
      </EntityModal>
    </>
  );
};
