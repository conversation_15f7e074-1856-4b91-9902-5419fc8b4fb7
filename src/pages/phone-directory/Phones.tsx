import { DashboardContentHeader } from "../../components";
import { EntityModal } from "../../components/base/EntityModal";
import { EntityTable } from "../../components/base/EntityTable";
import { PhoneDirectoryForm } from "../../components/forms/PhoneDirectoryForm";
import { useEntityManager } from "../../hooks/useEntityManager";
import { usePhoneDirectory } from "../../hooks/useQuery";
import { PhoneDirectory } from "../../interfaces/phone-directory";

export const Phones: React.FC = () => {
  const { listQuery, createMutation, updateMutation, deleteMutation } =
    usePhoneDirectory();

  const {
    form,
    isModalOpen,
    editingEntity,
    showModal,
    handleCancel,
    handleOk,
    notificationContext,
  } = useEntityManager({
    createEntity: (data: PhoneDirectory) => {
      createMutation.mutateAsync(data);
    },
    updateEntity: (data: PhoneDirectory) => {
      updateMutation.mutateAsync(data);
    },
  });

  return (
    <>
      {notificationContext}
      <DashboardContentHeader
        title="Directorio telefónico"
        tooltipTitle="Registra un número"
        onClick={() => showModal()}
      />
      <EntityTable
        data={listQuery.data ?? []}
        mapToRow={(phoneDirectory) => ({
          ...phoneDirectory,
          key: phoneDirectory.id,
        })}
        baseColumns={[
          { title: "Nombre", dataIndex: "name", key: "name" },
          { title: "Número", dataIndex: "phoneNumber", key: "phoneNumber" },
        ]}
        onEdit={showModal}
        onDelete={deleteMutation.mutate}
      />
      <EntityModal
        title="Número"
        isModalOpen={isModalOpen}
        isEditMode={editingEntity !== null}
        handleOk={handleOk}
        handleCancel={handleCancel}
      >
        <PhoneDirectoryForm form={form} />
      </EntityModal>
    </>
  );
};
