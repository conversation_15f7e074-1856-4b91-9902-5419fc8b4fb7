{"name": "hoa-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@nestjs/mapped-types": "*", "@tanstack/react-query": "^5.67.1", "@tanstack/react-router": "^1.112.17", "antd": "^5.22.1", "axios": "^1.7.7", "mobx": "^6.13.6", "mobx-react-lite": "^4.1.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}